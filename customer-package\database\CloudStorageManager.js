/**
 * نظام التخزين السحابي المحلي
 * Local Cloud Storage System
 * مؤسسة وقود المستقبل - Future Fuel Corporation
 * 
 * يستخدم ملفات JSON محلية لتخزين مساحة غير محدودة تقريباً
 */

class CloudStorageManager {
    constructor() {
        this.storageType = 'file-system';
        this.baseDirectory = 'FutureFuelData';
        this.isSupported = false;
        this.fileHandle = null;
        this.directoryHandle = null;
        
        this.checkSupport();
    }

    /**
     * فحص دعم File System Access API
     */
    checkSupport() {
        if ('showDirectoryPicker' in window) {
            this.isSupported = true;
            console.log('✅ File System Access API مدعوم - مساحة غير محدودة متاحة');
        } else {
            console.log('⚠️ File System Access API غير مدعوم - سيتم استخدام التحميل/الرفع');
        }
    }

    /**
     * تهيئة مجلد التخزين
     */
    async initializeStorage() {
        if (!this.isSupported) {
            console.log('💾 سيتم استخدام نظام التحميل/الرفع للملفات');
            return false;
        }

        try {
            // طلب اختيار مجلد للتخزين
            this.directoryHandle = await window.showDirectoryPicker({
                mode: 'readwrite',
                startIn: 'documents'
            });
            
            console.log(`✅ تم تهيئة مجلد التخزين: ${this.directoryHandle.name}`);
            
            // إنشاء هيكل المجلدات
            await this.createDirectoryStructure();
            
            return true;
        } catch (error) {
            console.error('❌ خطأ في تهيئة التخزين:', error);
            return false;
        }
    }

    /**
     * إنشاء هيكل المجلدات
     */
    async createDirectoryStructure() {
        const folders = [
            'customers',
            'gasCards', 
            'transmissionTable',
            'employees',
            'products',
            'sales',
            'appointments',
            'files',
            'archive',
            'backups',
            'exports'
        ];

        for (let folder of folders) {
            try {
                await this.directoryHandle.getDirectoryHandle(folder, { create: true });
                console.log(`📁 تم إنشاء مجلد: ${folder}`);
            } catch (error) {
                console.error(`❌ خطأ في إنشاء مجلد ${folder}:`, error);
            }
        }
    }

    /**
     * حفظ بيانات في ملف
     */
    async saveToFile(tableName, data, fileName = null) {
        if (!fileName) {
            fileName = `${tableName}_${new Date().toISOString().split('T')[0]}.json`;
        }

        const jsonData = JSON.stringify(data, null, 2);

        if (this.isSupported && this.directoryHandle) {
            try {
                // الحصول على مجلد الجدول
                const tableDir = await this.directoryHandle.getDirectoryHandle(tableName, { create: true });
                
                // إنشاء/كتابة الملف
                const fileHandle = await tableDir.getFileHandle(fileName, { create: true });
                const writable = await fileHandle.createWritable();
                
                await writable.write(jsonData);
                await writable.close();
                
                console.log(`✅ تم حفظ ${fileName} في مجلد ${tableName}`);
                return true;
            } catch (error) {
                console.error('❌ خطأ في حفظ الملف:', error);
                return false;
            }
        } else {
            // استخدام التحميل التقليدي
            this.downloadFile(fileName, jsonData);
            return true;
        }
    }

    /**
     * قراءة بيانات من ملف
     */
    async loadFromFile(tableName, fileName) {
        if (!this.isSupported || !this.directoryHandle) {
            throw new Error('يجب رفع الملف يدوياً');
        }

        try {
            const tableDir = await this.directoryHandle.getDirectoryHandle(tableName);
            const fileHandle = await tableDir.getFileHandle(fileName);
            const file = await fileHandle.getFile();
            const text = await file.text();
            
            return JSON.parse(text);
        } catch (error) {
            console.error('❌ خطأ في قراءة الملف:', error);
            throw error;
        }
    }

    /**
     * الحصول على قائمة الملفات في جدول
     */
    async getFilesList(tableName) {
        if (!this.isSupported || !this.directoryHandle) {
            return [];
        }

        try {
            const tableDir = await this.directoryHandle.getDirectoryHandle(tableName);
            const files = [];
            
            for await (const [name, handle] of tableDir.entries()) {
                if (handle.kind === 'file') {
                    const file = await handle.getFile();
                    files.push({
                        name: name,
                        size: file.size,
                        lastModified: new Date(file.lastModified),
                        type: file.type
                    });
                }
            }
            
            return files;
        } catch (error) {
            console.error('❌ خطأ في الحصول على قائمة الملفات:', error);
            return [];
        }
    }

    /**
     * تحميل ملف (للمتصفحات غير المدعومة)
     */
    downloadFile(fileName, data) {
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.click();
        
        URL.revokeObjectURL(url);
        console.log(`📥 تم تحميل الملف: ${fileName}`);
    }

    /**
     * رفع ملف
     */
    async uploadFile() {
        return new Promise((resolve, reject) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = async (event) => {
                const file = event.target.files[0];
                if (!file) {
                    reject(new Error('لم يتم اختيار ملف'));
                    return;
                }
                
                try {
                    const text = await file.text();
                    const data = JSON.parse(text);
                    resolve({ fileName: file.name, data: data });
                } catch (error) {
                    reject(new Error('خطأ في قراءة الملف: ' + error.message));
                }
            };
            
            input.click();
        });
    }

    /**
     * حفظ نسخة احتياطية شاملة
     */
    async createFullBackup(allData) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFileName = `full_backup_${timestamp}.json`;
        
        const backupData = {
            timestamp: new Date().toISOString(),
            version: '1.0',
            tables: allData,
            metadata: {
                totalRecords: Object.values(allData).reduce((sum, table) => sum + (table.length || 0), 0),
                backupSize: JSON.stringify(allData).length
            }
        };
        
        return await this.saveToFile('backups', backupData, backupFileName);
    }

    /**
     * استعادة من نسخة احتياطية
     */
    async restoreFromBackup(fileName) {
        try {
            const backupData = await this.loadFromFile('backups', fileName);
            return backupData.tables;
        } catch (error) {
            console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
            throw error;
        }
    }

    /**
     * تصدير جدول إلى CSV
     */
    async exportToCSV(tableName, data) {
        if (!data || data.length === 0) {
            throw new Error('لا توجد بيانات للتصدير');
        }

        // تحويل إلى CSV
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => {
                    let value = row[header];
                    if (value === null || value === undefined) value = '';
                    if (typeof value === 'string' && value.includes(',')) {
                        value = `"${value}"`;
                    }
                    return value;
                }).join(',')
            )
        ].join('\n');

        const fileName = `${tableName}_export_${new Date().toISOString().split('T')[0]}.csv`;
        
        if (this.isSupported && this.directoryHandle) {
            return await this.saveToFile('exports', csvContent, fileName);
        } else {
            this.downloadFile(fileName, csvContent);
            return true;
        }
    }

    /**
     * تصدير جدول إلى Excel (XLSX)
     */
    async exportToExcel(tableName, data) {
        // تحويل بسيط إلى تنسيق Excel XML
        if (!data || data.length === 0) {
            throw new Error('لا توجد بيانات للتصدير');
        }

        const headers = Object.keys(data[0]);
        
        let xmlContent = `<?xml version="1.0"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
<Worksheet ss:Name="${tableName}">
<Table>
<Row>`;

        // إضافة الرؤوس
        headers.forEach(header => {
            xmlContent += `<Cell><Data ss:Type="String">${header}</Data></Cell>`;
        });
        xmlContent += '</Row>';

        // إضافة البيانات
        data.forEach(row => {
            xmlContent += '<Row>';
            headers.forEach(header => {
                let value = row[header];
                if (value === null || value === undefined) value = '';
                
                let dataType = 'String';
                if (typeof value === 'number') dataType = 'Number';
                
                xmlContent += `<Cell><Data ss:Type="${dataType}">${value}</Data></Cell>`;
            });
            xmlContent += '</Row>';
        });

        xmlContent += '</Table></Worksheet></Workbook>';

        const fileName = `${tableName}_export_${new Date().toISOString().split('T')[0]}.xls`;
        
        if (this.isSupported && this.directoryHandle) {
            return await this.saveToFile('exports', xmlContent, fileName);
        } else {
            this.downloadFile(fileName, xmlContent);
            return true;
        }
    }

    /**
     * ضغط البيانات
     */
    compressData(data) {
        // ضغط بسيط بإزالة المسافات والتكرار
        const compressed = JSON.stringify(data);
        const original = JSON.stringify(data, null, 2);
        
        const compressionRatio = ((original.length - compressed.length) / original.length * 100).toFixed(1);
        
        console.log(`🗜️ تم ضغط البيانات بنسبة ${compressionRatio}%`);
        
        return compressed;
    }

    /**
     * تقسيم البيانات الكبيرة
     */
    async splitLargeData(tableName, data, maxRecordsPerFile = 10000) {
        const chunks = [];
        
        for (let i = 0; i < data.length; i += maxRecordsPerFile) {
            const chunk = data.slice(i, i + maxRecordsPerFile);
            const chunkNumber = Math.floor(i / maxRecordsPerFile) + 1;
            const fileName = `${tableName}_part_${chunkNumber}.json`;
            
            await this.saveToFile(tableName, chunk, fileName);
            chunks.push(fileName);
        }
        
        console.log(`📦 تم تقسيم ${data.length} سجل إلى ${chunks.length} ملف`);
        return chunks;
    }

    /**
     * دمج الملفات المقسمة
     */
    async mergeSplitFiles(tableName, fileNames) {
        let mergedData = [];
        
        for (let fileName of fileNames) {
            try {
                const chunkData = await this.loadFromFile(tableName, fileName);
                mergedData = mergedData.concat(chunkData);
            } catch (error) {
                console.error(`❌ خطأ في قراءة ${fileName}:`, error);
            }
        }
        
        console.log(`🔗 تم دمج ${fileNames.length} ملف إلى ${mergedData.length} سجل`);
        return mergedData;
    }

    /**
     * حساب مساحة التخزين المستخدمة
     */
    async calculateStorageUsage() {
        if (!this.isSupported || !this.directoryHandle) {
            return { error: 'غير مدعوم' };
        }

        let totalSize = 0;
        const folderSizes = {};

        try {
            for await (const [name, handle] of this.directoryHandle.entries()) {
                if (handle.kind === 'directory') {
                    let folderSize = 0;
                    
                    for await (const [fileName, fileHandle] of handle.entries()) {
                        if (fileHandle.kind === 'file') {
                            const file = await fileHandle.getFile();
                            folderSize += file.size;
                        }
                    }
                    
                    folderSizes[name] = folderSize;
                    totalSize += folderSize;
                }
            }

            return {
                totalSize: totalSize,
                totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
                folderSizes: folderSizes,
                isUnlimited: true
            };
        } catch (error) {
            console.error('❌ خطأ في حساب مساحة التخزين:', error);
            return { error: error.message };
        }
    }
}

// تصدير الكلاس
window.CloudStorageManager = CloudStorageManager;

console.log('✅ تم تحميل نظام التخزين السحابي المحلي');
