# دليل قاعدة البيانات الاحترافية
## Professional Database Guide
### مؤسسة وقود المستقبل - Future Fuel Corporation

---

## 🎯 نظرة عامة

تم تطوير نظام قاعدة بيانات احترافي متكامل لإدارة جميع عمليات مؤسسة وقود المستقبل، يتضمن:

- **11 جدول رئيسي** لتغطية جميع العمليات
- **نظام علاقات متقدم** بين الجداول
- **فهارس محسنة** لتسريع الاستعلامات
- **قيود وتحقق** من صحة البيانات
- **محفزات تلقائية** للعمليات
- **واجهة إدارة شاملة**
- **API متكامل** للتطبيقات

---

## 📊 هيكل قاعدة البيانات

### 1. جدول العملاء (customers)
```sql
- id (مفتاح أساسي)
- customerCode (رمز العميل - فريد)
- name (الاسم - مطلوب)
- phone (الهاتف - مطلوب)
- email (البريد الإلكتروني - فريد)
- address (العنوان)
- nationalId (رقم الهوية - فريد)
- customerType (نوع العميل: فرد/شركة)
- creditLimit (الحد الائتماني)
- currentBalance (الرصيد الحالي)
- status (الحالة: نشط/معطل/محظور)
- registrationDate (تاريخ التسجيل)
- lastUpdate (آخر تحديث)
- notes (ملاحظات)
```

### 2. جدول بطاقات الغاز (gasCards)
```sql
- id (مفتاح أساسي)
- cardNumber (رقم البطاقة - فريد)
- customerId (معرف العميل - مفتاح خارجي)
- cardType (نوع البطاقة: عادية/ذهبية/بلاتينية)
- balance (الرصيد)
- creditLimit (الحد الائتماني)
- status (الحالة: نشطة/معطلة/مفقودة/منتهية)
- issueDate (تاريخ الإصدار)
- expiryDate (تاريخ الانتهاء)
- lastUsed (آخر استخدام)
- securityCode (رمز الأمان)
- dailyLimit (الحد اليومي)
- monthlyLimit (الحد الشهري)
- notes (ملاحظات)
```

### 3. جدول الإرسال (transmissionTable)
```sql
- id (مفتاح أساسي)
- operationNumber (رقم العملية - فريد)
- operationType (نوع العملية: تركيب/مراقبة)
- tankNumber (رقم الخزان)
- carType (نوع السيارة)
- carModel (موديل السيارة)
- carYear (سنة الصنع)
- serialNumber (الرقم التسلسلي)
- registrationNumber (رقم التسجيل)
- ownerName (اسم المالك)
- ownerPhone (هاتف المالك)
- ownerNationalId (رقم هوية المالك)
- operationDate (تاريخ العملية)
- operationTime (وقت العملية)
- technicianId (معرف الفني)
- inspectorId (معرف المفتش)
- certificateNumber (رقم الشهادة)
- certificateDate (تاريخ الشهادة)
- validUntil (صالح حتى)
- operationCost (تكلفة العملية)
- paymentStatus (حالة الدفع)
- status (حالة العملية)
- notes (ملاحظات)
- createdAt (تاريخ الإنشاء)
- updatedAt (تاريخ التحديث)
```

### 4. جدول الموظفين (employees)
```sql
- id (مفتاح أساسي)
- employeeCode (رمز الموظف - فريد)
- name (الاسم)
- position (المنصب)
- department (القسم)
- phone (الهاتف)
- email (البريد الإلكتروني)
- nationalId (رقم الهوية)
- hireDate (تاريخ التوظيف)
- salary (الراتب)
- status (الحالة)
- permissions (الصلاحيات)
- lastLogin (آخر تسجيل دخول)
- notes (ملاحظات)
```

### 5. جدول المنتجات (products)
```sql
- id (مفتاح أساسي)
- productCode (رمز المنتج)
- name (الاسم)
- category (الفئة)
- type (النوع: منتج/خدمة)
- unit (الوحدة)
- costPrice (سعر التكلفة)
- sellingPrice (سعر البيع)
- currentStock (المخزون الحالي)
- minStock (الحد الأدنى للمخزون)
- maxStock (الحد الأقصى للمخزون)
- supplierId (معرف المورد)
- status (الحالة)
- description (الوصف)
- specifications (المواصفات)
- createdAt (تاريخ الإنشاء)
- updatedAt (تاريخ التحديث)
```

### 6. جدول الموردين (suppliers)
### 7. جدول المبيعات (sales)
### 8. جدول تفاصيل المبيعات (saleItems)
### 9. جدول المشتريات (purchases)
### 10. جدول المواعيد (appointments)
### 11. جدول المعاملات المالية (transactions)

---

## 🔗 العلاقات بين الجداول

### العلاقات الرئيسية:
1. **العملاء ← بطاقات الغاز** (واحد لمتعدد)
2. **العملاء ← المبيعات** (واحد لمتعدد)
3. **الموظفين ← المبيعات** (واحد لمتعدد)
4. **المبيعات ← تفاصيل المبيعات** (واحد لمتعدد)
5. **المنتجات ← تفاصيل المبيعات** (واحد لمتعدد)
6. **الموردين ← المنتجات** (واحد لمتعدد)

---

## 🚀 كيفية الاستخدام

### 1. تهيئة قاعدة البيانات
```javascript
// إنشاء مثيل جديد من قاعدة البيانات
const db = new DatabaseManager();

// تهيئة تلقائية للجداول والعلاقات
db.init();
```

### 2. العمليات الأساسية

#### إدراج بيانات جديدة:
```javascript
// إضافة عميل جديد
const customer = db.insert('customers', {
    name: 'أحمد محمد',
    phone: '0551234567',
    email: '<EMAIL>',
    customerType: 'فرد'
});

// إضافة عملية إرسال
const transmission = db.insert('transmissionTable', {
    operationType: 'تركيب',
    tankNumber: 'TK-2024-001',
    carType: 'تويوتا كورولا',
    serialNumber: 'SN123456',
    registrationNumber: '12345-67-89',
    ownerName: 'أحمد محمد'
});
```

#### استعلام البيانات:
```javascript
// جلب جميع العملاء
const customers = db.select('customers');

// البحث بشروط
const activeCustomers = db.select('customers', { status: 'نشط' });

// البحث المتقدم
const searchResults = db.select('customers', {
    name: { $like: 'أحمد' },
    registrationDate: { $gte: '2024-01-01' }
});

// ترتيب وتحديد النتائج
const recentCustomers = db.select('customers', {}, {
    orderBy: { field: 'registrationDate', direction: 'DESC' },
    limit: 10
});
```

#### تحديث البيانات:
```javascript
// تحديث بيانات عميل
db.update('customers', 1, {
    phone: '0559876543',
    address: 'الرياض، المملكة العربية السعودية'
});
```

#### حذف البيانات:
```javascript
// حذف عميل
db.delete('customers', 1);
```

### 3. الاستعلامات المتقدمة

#### الربط بين الجداول:
```javascript
// ربط العملاء ببطاقات الغاز
const customersWithCards = db.join('customers', 'gasCards', 'id=customerId');
```

#### التجميع والإحصائيات:
```javascript
// إحصائيات جدول الإرسال
const stats = db.aggregate('transmissionTable', [
    { $group: { 
        _id: 'operationType',
        count: { $sum: 1 },
        totalCost: { $sum: 'operationCost' }
    }}
]);
```

### 4. استخدام API

#### تهيئة API:
```javascript
const api = new DatabaseAPI();
```

#### استخدام نقاط النهاية:
```javascript
// جلب العملاء
const response = await api.handleRequest('customers', 'get', {
    page: 1,
    limit: 20,
    search: 'أحمد'
});

// إضافة عميل جديد
const newCustomer = await api.handleRequest('customers', 'post', {}, {
    name: 'محمد علي',
    phone: '0557654321',
    email: '<EMAIL>'
});

// إحصائيات جدول الإرسال
const transmissionStats = await api.handleRequest('transmissionTable', 'getStatistics');
```

---

## 🛠️ الميزات المتقدمة

### 1. النسخ الاحتياطي والاستعادة
```javascript
// إنشاء نسخة احتياطية
const backup = db.backup();

// استعادة نسخة احتياطية
db.restore(0); // استعادة آخر نسخة احتياطية
```

### 2. التحقق من صحة البيانات
```javascript
// التحقق التلقائي من:
// - الحقول المطلوبة
// - أنواع البيانات
// - القيم الفريدة
// - قيود ENUM
// - الحد الأدنى والأقصى
```

### 3. المحفزات التلقائية
```javascript
// تحديث تلقائي لـ:
// - تواريخ آخر تعديل
// - أرصدة العملاء
// - مخزون المنتجات
```

### 4. الفهارس المحسنة
```javascript
// فهارس تلقائية على:
// - المفاتيح الأساسية
// - المفاتيح الخارجية
// - الحقول الفريدة
// - حقول البحث الشائعة
```

---

## 📱 واجهة الإدارة

### الوصول لواجهة الإدارة:
افتح الملف: `database/DatabaseAdmin.html`

### الميزات المتاحة:
1. **نظرة عامة**: إحصائيات شاملة
2. **إدارة الجداول**: عرض وتعديل البيانات
3. **منشئ الاستعلامات**: إنشاء استعلامات مخصصة
4. **إدخال البيانات**: نماذج تلقائية لإدخال البيانات
5. **النسخ الاحتياطي**: إدارة النسخ الاحتياطية
6. **الصيانة**: أدوات تحسين وصيانة

---

## 🔧 الصيانة والتحسين

### 1. تحسين الأداء
```javascript
// تنظيف البيانات القديمة
db.cleanupOldData();

// ضغط البيانات
db.compressData();

// إعادة بناء الفهارس
db.rebuildIndexes();
```

### 2. مراقبة الأداء
```javascript
// إحصائيات الاستخدام
const stats = db.getStatistics();

// حجم قاعدة البيانات
console.log(`حجم قاعدة البيانات: ${stats.databaseSizeMB} MB`);

// عدد السجلات
console.log(`إجمالي السجلات: ${stats.totalRecords}`);
```

---

## 🚨 أفضل الممارسات

### 1. أمان البيانات
- ✅ استخدم النسخ الاحتياطية بانتظام
- ✅ تحقق من صحة البيانات قبل الإدراج
- ✅ استخدم المعاملات للعمليات المعقدة
- ✅ راقب حجم قاعدة البيانات

### 2. الأداء
- ✅ استخدم الفهارس للبحث السريع
- ✅ حدد النتائج بـ limit عند الحاجة
- ✅ استخدم الشروط المحددة في الاستعلامات
- ✅ نظف البيانات القديمة دورياً

### 3. التطوير
- ✅ استخدم API للوصول للبيانات
- ✅ تحقق من الأخطاء دائماً
- ✅ استخدم المحفزات للعمليات التلقائية
- ✅ وثق التغييرات في هيكل قاعدة البيانات

---

## 📞 الدعم والمساعدة

للحصول على المساعدة:
- 📧 البريد الإلكتروني: <EMAIL>
- 📞 الهاتف: +966-11-123-4567
- 🌐 الموقع: www.futurefuel.com

---

**قاعدة البيانات الاحترافية - مؤسسة وقود المستقبل**
**Professional Database System - Future Fuel Corporation**
