<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تكامل جدول الإرسال</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #2196f3;
        }
        .btn {
            background: #2196f3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #1976d2;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .info {
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار تكامل جدول الإرسال</h1>
        <p>هذه الصفحة لاختبار التكامل بين جدول الإرسال والنظام الرئيسي</p>

        <div class="test-section">
            <h3>1. اختبار تحميل البيانات</h3>
            <button class="btn" onclick="testDataLoading()">اختبار تحميل البيانات</button>
            <div id="data-loading-result"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار حفظ البيانات</h3>
            <button class="btn" onclick="testDataSaving()">اختبار حفظ البيانات</button>
            <div id="data-saving-result"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار فتح جدول الإرسال</h3>
            <button class="btn" onclick="testOpenTransmissionTable()">فتح جدول الإرسال</button>
            <div id="open-table-result"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار تحديث العدادات</h3>
            <button class="btn" onclick="testCountersUpdate()">اختبار العدادات</button>
            <div id="counters-result"></div>
        </div>

        <div class="test-section">
            <h3>5. اختبار الإعدادات</h3>
            <button class="btn" onclick="testConfigLoading()">اختبار الإعدادات</button>
            <div id="config-result"></div>
        </div>

        <div class="test-section">
            <h3>6. إضافة بيانات تجريبية</h3>
            <button class="btn" onclick="addSampleData()">إضافة بيانات تجريبية</button>
            <button class="btn" onclick="clearAllData()">مسح جميع البيانات</button>
            <div id="sample-data-result"></div>
        </div>
    </div>

    <script>
        // اختبار تحميل البيانات
        function testDataLoading() {
            const resultDiv = document.getElementById('data-loading-result');
            try {
                const transmissionData = JSON.parse(localStorage.getItem('transmissionTableData') || '[]');
                const mainData = JSON.parse(localStorage.getItem('gasShopData') || '{}');
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ تم تحميل البيانات بنجاح<br>
                        📊 عدد سجلات جدول الإرسال: ${transmissionData.length}<br>
                        📋 البيانات الرئيسية: ${mainData.transmissionTable ? mainData.transmissionTable.length : 0} سجل
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ خطأ في تحميل البيانات: ${error.message}
                    </div>
                `;
            }
        }

        // اختبار حفظ البيانات
        function testDataSaving() {
            const resultDiv = document.getElementById('data-saving-result');
            try {
                const testData = [
                    {
                        type: 'تركيب',
                        tankNumber: 'TEST-001',
                        carType: 'تويوتا كورولا',
                        serialNumber: 'SN123456',
                        registrationNumber: '12345-67-89',
                        ownerName: 'أحمد محمد',
                        operationDate: new Date().toISOString().split('T')[0]
                    }
                ];

                localStorage.setItem('transmissionTableData', JSON.stringify(testData));
                
                // تحديث البيانات الرئيسية
                const mainData = JSON.parse(localStorage.getItem('gasShopData') || '{}');
                mainData.transmissionTable = testData;
                localStorage.setItem('gasShopData', JSON.stringify(mainData));

                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ تم حفظ البيانات التجريبية بنجاح
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ خطأ في حفظ البيانات: ${error.message}
                    </div>
                `;
            }
        }

        // اختبار فتح جدول الإرسال
        function testOpenTransmissionTable() {
            const resultDiv = document.getElementById('open-table-result');
            try {
                window.open('transmission-table.html', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ تم فتح جدول الإرسال في نافذة جديدة
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ خطأ في فتح جدول الإرسال: ${error.message}
                    </div>
                `;
            }
        }

        // اختبار تحديث العدادات
        function testCountersUpdate() {
            const resultDiv = document.getElementById('counters-result');
            try {
                const transmissionData = JSON.parse(localStorage.getItem('transmissionTableData') || '[]');
                const now = new Date();
                const currentMonth = now.getMonth();
                const currentYear = now.getFullYear();
                
                const monthCount = transmissionData.filter(entry => {
                    const entryDate = new Date(entry.operationDate);
                    return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
                }).length;
                
                const totalCount = transmissionData.length;
                const installationCount = transmissionData.filter(entry => entry.type === 'تركيب').length;
                const monitoringCount = transmissionData.filter(entry => entry.type === 'مراقبة').length;

                resultDiv.innerHTML = `
                    <div class="result info">
                        📊 إحصائيات جدول الإرسال:<br>
                        📅 عمليات الشهر الحالي: ${monthCount}<br>
                        📋 إجمالي العمليات: ${totalCount}<br>
                        🔧 عمليات التركيب: ${installationCount}<br>
                        👁️ عمليات المراقبة: ${monitoringCount}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ خطأ في حساب العدادات: ${error.message}
                    </div>
                `;
            }
        }

        // اختبار تحميل الإعدادات
        function testConfigLoading() {
            const resultDiv = document.getElementById('config-result');
            try {
                const config = JSON.parse(localStorage.getItem('appConfig') || '{}');
                const transmissionConfig = config.transmissionTable || {};

                resultDiv.innerHTML = `
                    <div class="result info">
                        ⚙️ إعدادات جدول الإرسال:<br>
                        🏢 اسم الشركة: ${transmissionConfig.companyName || 'غير محدد'}<br>
                        📄 رقم الترخيص: ${transmissionConfig.licenseNumber || 'غير محدد'}<br>
                        🎯 الجهة المستهدفة: ${transmissionConfig.targetAuthority || 'غير محدد'}<br>
                        💾 الحفظ التلقائي: ${transmissionConfig.autoSave ? 'مفعل' : 'معطل'}
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ خطأ في تحميل الإعدادات: ${error.message}
                    </div>
                `;
            }
        }

        // إضافة بيانات تجريبية
        function addSampleData() {
            const resultDiv = document.getElementById('sample-data-result');
            try {
                const sampleData = [
                    {
                        type: 'تركيب',
                        tankNumber: 'TK-2024-001',
                        carType: 'تويوتا كورولا 2020',
                        serialNumber: 'SN789123',
                        registrationNumber: '12345-16-89',
                        ownerName: 'أحمد محمد علي',
                        operationDate: '2024-12-19'
                    },
                    {
                        type: 'مراقبة',
                        tankNumber: 'TK-2024-002',
                        carType: 'هيونداي إلنترا 2019',
                        serialNumber: 'SN456789',
                        registrationNumber: '67890-16-12',
                        ownerName: 'فاطمة أحمد',
                        operationDate: '2024-12-18'
                    },
                    {
                        type: 'تركيب',
                        tankNumber: 'TK-2024-003',
                        carType: 'نيسان صني 2021',
                        serialNumber: 'SN321654',
                        registrationNumber: '54321-16-98',
                        ownerName: 'محمد عبد الله',
                        operationDate: '2024-12-17'
                    }
                ];

                localStorage.setItem('transmissionTableData', JSON.stringify(sampleData));
                
                // تحديث البيانات الرئيسية
                const mainData = JSON.parse(localStorage.getItem('gasShopData') || '{}');
                mainData.transmissionTable = sampleData;
                localStorage.setItem('gasShopData', JSON.stringify(mainData));

                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ تم إضافة ${sampleData.length} سجلات تجريبية بنجاح
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ خطأ في إضافة البيانات التجريبية: ${error.message}
                    </div>
                `;
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            const resultDiv = document.getElementById('sample-data-result');
            if (confirm('هل أنت متأكد من مسح جميع بيانات جدول الإرسال؟')) {
                try {
                    localStorage.removeItem('transmissionTableData');
                    
                    // مسح من البيانات الرئيسية
                    const mainData = JSON.parse(localStorage.getItem('gasShopData') || '{}');
                    delete mainData.transmissionTable;
                    localStorage.setItem('gasShopData', JSON.stringify(mainData));

                    resultDiv.innerHTML = `
                        <div class="result info">
                            🗑️ تم مسح جميع بيانات جدول الإرسال
                        </div>
                    `;
                } catch (error) {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ خطأ في مسح البيانات: ${error.message}
                        </div>
                    `;
                }
            }
        }

        // تشغيل اختبارات أولية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 صفحة اختبار تكامل جدول الإرسال جاهزة');
        });
    </script>
</body>
</html>
