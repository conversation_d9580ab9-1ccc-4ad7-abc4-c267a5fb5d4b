<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المساحة الكبيرة - مؤسسة وقود المستقبل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
            margin: 0;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #00bcd4, #009688);
            color: white;
            border-radius: 10px;
        }
        .storage-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .storage-card {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .storage-card:hover {
            border-color: #00bcd4;
            transform: translateY(-2px);
        }
        .storage-card.active {
            border-color: #00bcd4;
            background: #e0f7fa;
        }
        .storage-card h3 {
            margin-top: 0;
            color: #00bcd4;
            font-size: 20px;
        }
        .capacity-info {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .capacity-number {
            font-size: 28px;
            font-weight: bold;
            color: #00bcd4;
        }
        .capacity-label {
            color: #666;
            margin-top: 5px;
        }
        .btn {
            background: #00bcd4;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background 0.3s ease;
        }
        .btn:hover {
            background: #00acc1;
        }
        .btn.success {
            background: #4caf50;
        }
        .btn.warning {
            background: #ff9800;
        }
        .btn.danger {
            background: #f44336;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-online {
            background: #4caf50;
            animation: pulse 2s infinite;
        }
        .status-offline {
            background: #f44336;
        }
        .status-warning {
            background: #ff9800;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .features-list {
            list-style: none;
            padding: 0;
        }
        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .features-list li:before {
            content: "✅";
            margin-left: 10px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 15px;
            text-align: center;
        }
        .comparison-table th {
            background: #00bcd4;
            color: white;
            font-weight: bold;
        }
        .comparison-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .alert.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .alert.warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .alert.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .alert.info {
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #2196f3;
        }
        .progress-section {
            margin: 30px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 10px;
        }
        .migration-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .step-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #00bcd4;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .step-number {
            background: #00bcd4;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-expand-arrows-alt"></i> إدارة المساحة الكبيرة</h1>
            <p>حلول متقدمة لزيادة مساحة التخزين إلى مئات الجيجابايت</p>
        </div>

        <!-- خيارات التخزين -->
        <div class="storage-options">
            <!-- IndexedDB -->
            <div class="storage-card" id="indexeddb-card">
                <h3>
                    <span class="status-indicator" id="indexeddb-status"></span>
                    قاعدة بيانات IndexedDB
                </h3>
                
                <div class="capacity-info">
                    <div class="capacity-number" id="indexeddb-capacity">50% من القرص</div>
                    <div class="capacity-label">المساحة المتاحة</div>
                </div>
                
                <ul class="features-list">
                    <li>مساحة تصل إلى مئات الجيجابايت</li>
                    <li>أداء سريع جداً</li>
                    <li>فهرسة متقدمة</li>
                    <li>استعلامات معقدة</li>
                    <li>حفظ الملفات الكبيرة</li>
                </ul>
                
                <div style="margin-top: 20px;">
                    <button class="btn success" onclick="initializeIndexedDB()">
                        <i class="fas fa-rocket"></i> تفعيل IndexedDB
                    </button>
                    <button class="btn" onclick="testIndexedDB()" id="test-indexeddb" disabled>
                        <i class="fas fa-flask"></i> اختبار
                    </button>
                </div>
            </div>

            <!-- التخزين السحابي المحلي -->
            <div class="storage-card" id="cloud-card">
                <h3>
                    <span class="status-indicator" id="cloud-status"></span>
                    التخزين السحابي المحلي
                </h3>
                
                <div class="capacity-info">
                    <div class="capacity-number" id="cloud-capacity">غير محدود</div>
                    <div class="capacity-label">المساحة المتاحة</div>
                </div>
                
                <ul class="features-list">
                    <li>مساحة غير محدودة تقريباً</li>
                    <li>ملفات منفصلة سهلة الإدارة</li>
                    <li>نسخ احتياطية تلقائية</li>
                    <li>تصدير متعدد الصيغ</li>
                    <li>مشاركة الملفات</li>
                </ul>
                
                <div style="margin-top: 20px;">
                    <button class="btn success" onclick="initializeCloudStorage()">
                        <i class="fas fa-cloud"></i> تفعيل التخزين السحابي
                    </button>
                    <button class="btn" onclick="testCloudStorage()" id="test-cloud" disabled>
                        <i class="fas fa-flask"></i> اختبار
                    </button>
                </div>
            </div>

            <!-- النظام المختلط -->
            <div class="storage-card" id="hybrid-card">
                <h3>
                    <span class="status-indicator" id="hybrid-status"></span>
                    النظام المختلط (الأفضل)
                </h3>
                
                <div class="capacity-info">
                    <div class="capacity-number" id="hybrid-capacity">الأفضل من الاثنين</div>
                    <div class="capacity-label">المساحة المتاحة</div>
                </div>
                
                <ul class="features-list">
                    <li>البيانات النشطة في IndexedDB</li>
                    <li>الأرشيف في التخزين السحابي</li>
                    <li>أداء ممتاز + مساحة كبيرة</li>
                    <li>أرشفة تلقائية ذكية</li>
                    <li>استرداد سريع</li>
                </ul>
                
                <div style="margin-top: 20px;">
                    <button class="btn success" onclick="initializeHybridSystem()">
                        <i class="fas fa-magic"></i> تفعيل النظام المختلط
                    </button>
                    <button class="btn" onclick="testHybridSystem()" id="test-hybrid" disabled>
                        <i class="fas fa-flask"></i> اختبار شامل
                    </button>
                </div>
            </div>
        </div>

        <!-- جدول المقارنة -->
        <div style="margin: 40px 0;">
            <h3>📊 مقارنة أنظمة التخزين</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>localStorage<br>(النظام الحالي)</th>
                        <th>IndexedDB</th>
                        <th>التخزين السحابي المحلي</th>
                        <th>النظام المختلط</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>المساحة القصوى</strong></td>
                        <td>5-10 MB</td>
                        <td>50% من القرص الصلب</td>
                        <td>غير محدود</td>
                        <td>غير محدود</td>
                    </tr>
                    <tr>
                        <td><strong>عدد الزبائن</strong></td>
                        <td>~11,000</td>
                        <td>~2,000,000</td>
                        <td>غير محدود</td>
                        <td>غير محدود</td>
                    </tr>
                    <tr>
                        <td><strong>عدد البطاقات</strong></td>
                        <td>~16,500</td>
                        <td>~3,300,000</td>
                        <td>غير محدود</td>
                        <td>غير محدود</td>
                    </tr>
                    <tr>
                        <td><strong>عدد العمليات</strong></td>
                        <td>~12,500</td>
                        <td>~2,500,000</td>
                        <td>غير محدود</td>
                        <td>غير محدود</td>
                    </tr>
                    <tr>
                        <td><strong>سرعة الأداء</strong></td>
                        <td>سريع</td>
                        <td>سريع جداً</td>
                        <td>متوسط</td>
                        <td>ممتاز</td>
                    </tr>
                    <tr>
                        <td><strong>النسخ الاحتياطية</strong></td>
                        <td>يدوي</td>
                        <td>برمجي</td>
                        <td>تلقائي</td>
                        <td>تلقائي ذكي</td>
                    </tr>
                    <tr>
                        <td><strong>سهولة الإدارة</strong></td>
                        <td>بسيط</td>
                        <td>متقدم</td>
                        <td>سهل</td>
                        <td>ذكي</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- خطوات الترقية -->
        <div class="progress-section">
            <h3>🚀 خطوات الترقية للمساحة الكبيرة</h3>
            <div class="migration-steps">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h4>النسخ الاحتياطي</h4>
                    <p>إنشاء نسخة احتياطية من البيانات الحالية</p>
                    <button class="btn" onclick="createBackupBeforeMigration()">
                        <i class="fas fa-save"></i> إنشاء نسخة احتياطية
                    </button>
                </div>
                
                <div class="step-card">
                    <div class="step-number">2</div>
                    <h4>اختيار النظام</h4>
                    <p>اختر نظام التخزين المناسب لاحتياجاتك</p>
                    <button class="btn" onclick="showSystemRecommendation()">
                        <i class="fas fa-lightbulb"></i> احصل على توصية
                    </button>
                </div>
                
                <div class="step-card">
                    <div class="step-number">3</div>
                    <h4>الترقية</h4>
                    <p>ترقية النظام ونقل البيانات</p>
                    <button class="btn" onclick="startMigration()">
                        <i class="fas fa-upload"></i> بدء الترقية
                    </button>
                </div>
                
                <div class="step-card">
                    <div class="step-number">4</div>
                    <h4>التحقق</h4>
                    <p>التحقق من سلامة البيانات والنظام</p>
                    <button class="btn" onclick="verifyMigration()">
                        <i class="fas fa-check-circle"></i> التحقق
                    </button>
                </div>
            </div>
        </div>

        <!-- منطقة النتائج -->
        <div id="results-area"></div>
    </div>

    <!-- تحميل ملفات قاعدة البيانات -->
    <script src="IndexedDBManager.js"></script>
    <script src="CloudStorageManager.js"></script>
    <script>
        // متغيرات النظام
        let indexedDBManager = null;
        let cloudStorageManager = null;
        let currentSystem = 'localStorage';

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkCurrentSystem();
            updateStorageStatus();
        });

        // فحص النظام الحالي
        function checkCurrentSystem() {
            // فحص دعم IndexedDB
            if ('indexedDB' in window) {
                document.getElementById('indexeddb-status').className = 'status-indicator status-warning';
            } else {
                document.getElementById('indexeddb-status').className = 'status-indicator status-offline';
            }

            // فحص دعم File System Access API
            if ('showDirectoryPicker' in window) {
                document.getElementById('cloud-status').className = 'status-indicator status-warning';
            } else {
                document.getElementById('cloud-status').className = 'status-indicator status-offline';
            }

            document.getElementById('hybrid-status').className = 'status-indicator status-warning';
        }

        // تحديث حالة التخزين
        async function updateStorageStatus() {
            // فحص المساحة المتاحة لـ IndexedDB
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                try {
                    const estimate = await navigator.storage.estimate();
                    const quotaGB = (estimate.quota / 1024 / 1024 / 1024).toFixed(1);
                    document.getElementById('indexeddb-capacity').textContent = quotaGB + ' GB';
                } catch (error) {
                    console.error('خطأ في فحص المساحة:', error);
                }
            }
        }

        // تفعيل IndexedDB
        async function initializeIndexedDB() {
            showAlert('جاري تهيئة قاعدة بيانات IndexedDB...', 'info');
            
            try {
                indexedDBManager = new IndexedDBManager();
                await indexedDBManager.init();
                
                document.getElementById('indexeddb-status').className = 'status-indicator status-online';
                document.getElementById('indexeddb-card').classList.add('active');
                document.getElementById('test-indexeddb').disabled = false;
                
                const stats = await indexedDBManager.getStorageStats();
                showAlert(`تم تفعيل IndexedDB بنجاح! المساحة المتاحة: ${stats.availableGB} GB`, 'success');
                
            } catch (error) {
                showAlert('خطأ في تفعيل IndexedDB: ' + error.message, 'error');
            }
        }

        // تفعيل التخزين السحابي المحلي
        async function initializeCloudStorage() {
            showAlert('جاري تهيئة التخزين السحابي المحلي...', 'info');
            
            try {
                cloudStorageManager = new CloudStorageManager();
                const success = await cloudStorageManager.initializeStorage();
                
                if (success) {
                    document.getElementById('cloud-status').className = 'status-indicator status-online';
                    document.getElementById('cloud-card').classList.add('active');
                    document.getElementById('test-cloud').disabled = false;
                    
                    showAlert('تم تفعيل التخزين السحابي المحلي بنجاح! مساحة غير محدودة متاحة', 'success');
                } else {
                    showAlert('تم تفعيل وضع التحميل/الرفع للملفات', 'warning');
                }
                
            } catch (error) {
                showAlert('خطأ في تفعيل التخزين السحابي: ' + error.message, 'error');
            }
        }

        // تفعيل النظام المختلط
        async function initializeHybridSystem() {
            showAlert('جاري تهيئة النظام المختلط...', 'info');
            
            try {
                // تفعيل كلا النظامين
                await initializeIndexedDB();
                await initializeCloudStorage();
                
                document.getElementById('hybrid-status').className = 'status-indicator status-online';
                document.getElementById('hybrid-card').classList.add('active');
                document.getElementById('test-hybrid').disabled = false;
                
                showAlert('تم تفعيل النظام المختلط بنجاح! أفضل أداء ومساحة غير محدودة', 'success');
                
            } catch (error) {
                showAlert('خطأ في تفعيل النظام المختلط: ' + error.message, 'error');
            }
        }

        // اختبار IndexedDB
        async function testIndexedDB() {
            if (!indexedDBManager) {
                showAlert('يجب تفعيل IndexedDB أولاً', 'warning');
                return;
            }

            showAlert('جاري اختبار IndexedDB...', 'info');
            
            try {
                // إضافة بيانات تجريبية
                const testCustomer = await indexedDBManager.insert('customers', {
                    customerCode: 'TEST001',
                    name: 'عميل تجريبي',
                    phone: '0551234567',
                    email: '<EMAIL>'
                });

                // قراءة البيانات
                const customers = await indexedDBManager.select('customers', { customerCode: 'TEST001' });
                
                // حذف البيانات التجريبية
                await indexedDBManager.delete('customers', testCustomer.id);
                
                const stats = await indexedDBManager.getStorageStats();
                
                showAlert(`اختبار IndexedDB نجح! يمكن حفظ ملايين السجلات. المساحة المتاحة: ${stats.availableGB} GB`, 'success');
                
            } catch (error) {
                showAlert('فشل اختبار IndexedDB: ' + error.message, 'error');
            }
        }

        // اختبار التخزين السحابي
        async function testCloudStorage() {
            if (!cloudStorageManager) {
                showAlert('يجب تفعيل التخزين السحابي أولاً', 'warning');
                return;
            }

            showAlert('جاري اختبار التخزين السحابي...', 'info');
            
            try {
                // إنشاء بيانات تجريبية
                const testData = [
                    { id: 1, name: 'عميل تجريبي 1', phone: '0551111111' },
                    { id: 2, name: 'عميل تجريبي 2', phone: '0552222222' }
                ];

                // حفظ في ملف
                const success = await cloudStorageManager.saveToFile('customers', testData, 'test_customers.json');
                
                if (success) {
                    const usage = await cloudStorageManager.calculateStorageUsage();
                    showAlert(`اختبار التخزين السحابي نجح! مساحة غير محدودة متاحة`, 'success');
                } else {
                    showAlert('تم تحميل ملف تجريبي - النظام يعمل بوضع التحميل/الرفع', 'warning');
                }
                
            } catch (error) {
                showAlert('فشل اختبار التخزين السحابي: ' + error.message, 'error');
            }
        }

        // عرض رسالة
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${type}`;
            alertDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
            
            const resultsArea = document.getElementById('results-area');
            resultsArea.appendChild(alertDiv);
            
            // التمرير للرسالة
            alertDiv.scrollIntoView({ behavior: 'smooth' });
            
            // إزالة الرسالة بعد 10 ثوانٍ
            setTimeout(() => {
                alertDiv.remove();
            }, 10000);
        }

        console.log('✅ تم تحميل واجهة إدارة المساحة الكبيرة');
    </script>
</body>
</html>
