<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - Remote Control Panel</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            direction: rtl;
            color: #e0e6ed;
            overflow-x: hidden;
        }

        /* الرأس الرئيسي */
        .main-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 1.5rem 2rem;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.3);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-logo {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .header-info {
            text-align: left;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        /* شريط الإحصائيات */
        .stats-bar {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .stat-card.licenses .stat-number { color: #3498db; }
        .stat-card.devices .stat-number { color: #27ae60; }
        .stat-card.active .stat-number { color: #f39c12; }
        .stat-card.expired .stat-number { color: #e74c3c; }

        /* المحتوى الرئيسي */
        .main-container {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        /* شبكة الأدوات */
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .tool-panel {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .panel-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .panel-header.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .panel-header.success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .panel-header.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .panel-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .panel-title {
            font-size: 1.3rem;
            font-weight: 700;
        }

        .panel-body {
            padding: 2rem;
        }

        /* النماذج */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #e0e6ed;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #e0e6ed;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3498db;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-input::placeholder {
            color: rgba(224, 230, 237, 0.5);
        }

        /* الأزرار */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            background: #7f8c8d;
            cursor: not-allowed;
            transform: none;
        }

        .btn-full {
            width: 100%;
            justify-content: center;
        }

        /* جداول البيانات */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            overflow: hidden;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem;
            text-align: right;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .data-table th {
            background: rgba(255, 255, 255, 0.1);
            font-weight: 600;
            font-size: 0.9rem;
        }

        .data-table td {
            font-size: 0.85rem;
        }

        .data-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* حالات المستخدمين */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-online {
            background: rgba(39, 174, 96, 0.2);
            color: #27ae60;
            border: 1px solid #27ae60;
        }

        .status-offline {
            background: rgba(149, 165, 166, 0.2);
            color: #95a5a6;
            border: 1px solid #95a5a6;
        }

        .status-suspended {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }

        .status-expired {
            background: rgba(243, 156, 18, 0.2);
            color: #f39c12;
            border: 1px solid #f39c12;
        }

        /* أزرار الإجراءات */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn.activate {
            background: #27ae60;
            color: white;
        }

        .action-btn.suspend {
            background: #e74c3c;
            color: white;
        }

        .action-btn.delete {
            background: #c0392b;
            color: white;
        }

        .action-btn.info {
            background: #3498db;
            color: white;
        }

        .action-btn:hover {
            transform: scale(1.05);
        }

        /* إشعارات */
        .notification {
            position: fixed;
            top: 20px;
            left: 20px;
            background: white;
            color: #2c3e50;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 10000;
            transform: translateX(-400px);
            opacity: 0;
            transition: all 0.4s ease;
            min-width: 320px;
            border-left: 5px solid #3498db;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification.success { border-left-color: #27ae60; }
        .notification.error { border-left-color: #e74c3c; }
        .notification.warning { border-left-color: #f39c12; }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* تحميل */
        .loading-spinner {
            display: none;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .tools-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .main-container {
                padding: 0 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- الرأس الرئيسي -->
    <header class="main-header">
        <div class="header-content">
            <div class="header-title">
                <div class="header-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div>
                    <h1 style="font-size: 1.5rem; font-weight: 700;">لوحة التحكم الإدارية</h1>
                    <div style="font-size: 0.9rem; opacity: 0.8;">Remote Control & License Management</div>
                </div>
            </div>
            <div class="header-info">
                <div>المدير: Admin</div>
                <div>آخر دخول: <span id="lastLogin">الآن</span></div>
            </div>
        </div>
    </header>

    <!-- شريط الإحصائيات -->
    <div class="stats-bar">
        <div class="stats-grid">
            <div class="stat-card licenses">
                <div class="stat-number" id="totalLicenses">0</div>
                <div class="stat-label">إجمالي التراخيص</div>
            </div>
            <div class="stat-card devices">
                <div class="stat-number" id="totalDevices">0</div>
                <div class="stat-label">الأجهزة المسجلة</div>
            </div>
            <div class="stat-card active">
                <div class="stat-number" id="activeUsers">0</div>
                <div class="stat-label">المستخدمون النشطون</div>
            </div>
            <div class="stat-card expired">
                <div class="stat-number" id="expiredLicenses">0</div>
                <div class="stat-label">التراخيص المنتهية</div>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-container">
        <!-- شبكة الأدوات -->
        <div class="tools-grid">
            <!-- لوحة توليد التراخيص -->
            <div class="tool-panel">
                <div class="panel-header">
                    <div class="panel-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="panel-title">توليد التراخيص</div>
                </div>
                <div class="panel-body">
                    <form id="licenseGeneratorForm">
                        <div class="form-group">
                            <label class="form-label">نوع الترخيص</label>
                            <select class="form-select" id="licenseType">
                                <option value="demo">تجريبي (30 يوم)</option>
                                <option value="monthly">شهري</option>
                                <option value="yearly">سنوي</option>
                                <option value="lifetime">مدى الحياة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">عدد التراخيص</label>
                            <input type="number" class="form-input" id="licenseCount" value="1" min="1" max="100">
                        </div>
                        <div class="form-group">
                            <label class="form-label">ملاحظات</label>
                            <input type="text" class="form-input" id="licenseNotes" placeholder="ملاحظات اختيارية">
                        </div>
                        <button type="submit" class="btn btn-primary btn-full">
                            <i class="fas fa-plus"></i>
                            توليد التراخيص
                            <div class="loading-spinner" id="generateSpinner"></div>
                        </button>
                    </form>

                    <div id="generatedLicenses" style="margin-top: 1rem; display: none;">
                        <h4 style="margin-bottom: 0.5rem; color: #3498db;">التراخيص المولدة:</h4>
                        <div id="licensesList"></div>
                    </div>
                </div>
            </div>

            <!-- لوحة التحكم عن بُعد -->
            <div class="tool-panel">
                <div class="panel-header danger">
                    <div class="panel-icon">
                        <i class="fas fa-satellite-dish"></i>
                    </div>
                    <div class="panel-title">التحكم عن بُعد</div>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <label class="form-label">معرف الجهاز المستهدف</label>
                        <input type="text" class="form-input" id="targetDeviceId" placeholder="أدخل معرف الجهاز">
                    </div>

                    <div class="action-buttons" style="margin-bottom: 1rem;">
                        <button class="btn btn-success" onclick="remoteActivate()">
                            <i class="fas fa-play"></i> تفعيل
                        </button>
                        <button class="btn btn-warning" onclick="remoteSuspend()">
                            <i class="fas fa-pause"></i> إيقاف مؤقت
                        </button>
                        <button class="btn btn-danger" onclick="remoteShutdown()">
                            <i class="fas fa-power-off"></i> إيقاف نهائي
                        </button>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="getDeviceInfo()">
                            <i class="fas fa-info-circle"></i> معلومات الجهاز
                        </button>
                        <button class="btn btn-danger" onclick="deleteDevice()">
                            <i class="fas fa-trash"></i> حذف الجهاز
                        </button>
                    </div>
                </div>
            </div>

            <!-- لوحة التفعيل المباشر للعملاء -->
            <div class="tool-panel">
                <div class="panel-header success">
                    <div class="panel-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="panel-title">تفعيل العملاء مباشرة من جهازك</div>
                </div>
                <div class="panel-body">
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #27ae60;">
                        <h4 style="margin: 0 0 10px 0; color: #27ae60;">
                            <i class="fas fa-info-circle"></i> كيفية التفعيل عن بُعد
                        </h4>
                        <p style="margin: 0; font-size: 0.9rem; line-height: 1.5;">
                            1. اطلب من العميل فتح التطبيق والوصول لصفحة تسجيل الدخول<br>
                            2. اطلب منه نسخ معرف الجهاز الظاهر في الصفحة<br>
                            3. أدخل معرف الجهاز هنا واختر نوع الترخيص<br>
                            4. اضغط "تفعيل مباشرة" وسيتم إنشاء ترخيص وربطه بجهازه<br>
                            5. أرسل كود الترخيص للعميل ليدخله في التطبيق
                        </p>
                    </div>

                    <div class="form-group">
                        <label class="form-label">معرف جهاز العميل</label>
                        <input type="text" class="form-input" id="customerDeviceId" placeholder="مثال: DEV-ABC123-XYZ789-456">
                        <small style="color: #666; font-size: 0.8rem;">يحصل عليه العميل من صفحة تسجيل الدخول</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label">نوع الترخيص</label>
                        <select class="form-input" id="customerLicenseType" onchange="updateLicenseDuration()">
                            <option value="demo">تجريبي (30 يوم مجاني)</option>
                            <option value="monthly">شهري (299 ريال)</option>
                            <option value="yearly">سنوي (2999 ريال)</option>
                            <option value="lifetime">مدى الحياة (9999 ريال)</option>
                            <option value="admin">إداري (للمطورين)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">مدة الترخيص (بالأيام)</label>
                        <input type="number" class="form-input" id="customerLicenseDuration" value="30" min="1" max="3650">
                    </div>

                    <div class="form-group">
                        <label class="form-label">اسم العميل (اختياري)</label>
                        <input type="text" class="form-input" id="customerName" placeholder="اسم العميل للمرجع">
                    </div>

                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea class="form-input" id="customerNotes" placeholder="ملاحظات حول العميل أو الترخيص" rows="2"></textarea>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary btn-full" onclick="activateCustomerDirectly()" style="font-size: 1.1rem; padding: 15px;">
                            <i class="fas fa-magic"></i> تفعيل العميل مباشرة من جهازي
                        </button>
                    </div>

                    <div id="activationResult" style="margin-top: 1rem;"></div>
                </div>
            </div>

            <!-- لوحة إدارة المستخدمين -->
            <div class="tool-panel">
                <div class="panel-header success">
                    <div class="panel-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="panel-title">إدارة المستخدمين</div>
                </div>
                <div class="panel-body">
                    <div class="form-group">
                        <button class="btn btn-primary btn-full" onclick="refreshUserData()">
                            <i class="fas fa-sync"></i> تحديث بيانات المستخدمين
                        </button>
                    </div>

                    <div class="form-group">
                        <label class="form-label">البحث عن مستخدم</label>
                        <input type="text" class="form-input" id="userSearch" placeholder="ابحث بمعرف الجهاز أو الترخيص" oninput="searchUsers()">
                    </div>
                </div>
            </div>

            <!-- لوحة النسخ الاحتياطي -->
            <div class="tool-panel">
                <div class="panel-header warning">
                    <div class="panel-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="panel-title">النسخ الاحتياطي</div>
                </div>
                <div class="panel-body">
                    <div class="action-buttons" style="margin-bottom: 1rem;">
                        <button class="btn btn-primary" onclick="createBackup()">
                            <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                        </button>
                        <button class="btn btn-warning" onclick="document.getElementById('backupFile').click()">
                            <i class="fas fa-upload"></i> استعادة نسخة احتياطية
                        </button>
                    </div>

                    <input type="file" id="backupFile" accept=".json" style="display: none;" onchange="restoreBackup(this)">

                    <div class="action-buttons">
                        <button class="btn btn-danger" onclick="clearAllData()">
                            <i class="fas fa-exclamation-triangle"></i> مسح جميع البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول المستخدمين والأجهزة -->
        <div class="tool-panel" style="margin-top: 2rem;">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-table"></i>
                </div>
                <div class="panel-title">المستخدمون والأجهزة المتصلة</div>
            </div>
            <div class="panel-body">
                <table class="data-table" id="usersTable">
                    <thead>
                        <tr>
                            <th>معرف الجهاز</th>
                            <th>كود الترخيص</th>
                            <th>نوع الترخيص</th>
                            <th>الحالة</th>
                            <th>آخر نشاط</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 2rem; opacity: 0.6;">
                                لا توجد بيانات متاحة
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // بيانات النظام
        let systemData = {
            licenses: [],
            devices: [],
            users: [],
            sessions: []
        };

        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystem();
            loadSystemData();
            updateStatistics();
            loadUsersTable();

            // ربط الأحداث
            document.getElementById('licenseGeneratorForm').addEventListener('submit', handleLicenseGeneration);

            // تحديث الوقت
            document.getElementById('lastLogin').textContent = new Date().toLocaleString('ar-SA');

            // تحديث دوري للبيانات
            setInterval(updateStatistics, 30000); // كل 30 ثانية
        });

        // تهيئة النظام
        function initializeSystem() {
            // تحميل البيانات من التخزين المحلي
            const storedLicenses = localStorage.getItem('validLicenses');
            const storedDevices = localStorage.getItem('devices');
            const storedSessions = localStorage.getItem('activeSessions');

            systemData.licenses = storedLicenses ? JSON.parse(storedLicenses) : [];
            systemData.devices = storedDevices ? JSON.parse(storedDevices) : [];
            systemData.sessions = storedSessions ? JSON.parse(storedSessions) : [];

            // إضافة بيانات تجريبية إذا لم تكن موجودة
            if (systemData.licenses.length === 0) {
                addDefaultLicenses();
            }
        }

        // إضافة تراخيص افتراضية
        function addDefaultLicenses() {
            const defaultLicenses = [
                {
                    code: 'DEMO-2024-TEST-0001',
                    type: 'demo',
                    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString(),
                    notes: 'ترخيص تجريبي افتراضي'
                },
                {
                    code: 'FULL-2024-PROD-0001',
                    type: 'yearly',
                    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: null,
                    activatedAt: null,
                    createdAt: new Date().toISOString(),
                    notes: 'ترخيص سنوي افتراضي'
                }
            ];

            systemData.licenses = defaultLicenses;
            localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
        }

        // تحميل بيانات النظام
        function loadSystemData() {
            // محاكاة تحميل البيانات من الخادم
            console.log('تحميل بيانات النظام...');

            // تحديث بيانات المستخدمين النشطين
            updateActiveUsers();
        }

        // تحديث المستخدمين النشطين
        function updateActiveUsers() {
            const activeLicenses = systemData.licenses.filter(l => l.deviceId && l.isActive);
            systemData.users = activeLicenses.map(license => ({
                deviceId: license.deviceId,
                licenseCode: license.code,
                licenseType: license.type,
                status: getDeviceStatus(license.deviceId),
                lastActivity: license.activatedAt || new Date().toISOString(),
                expiresAt: license.expiresAt,
                isOnline: Math.random() > 0.3 // محاكاة الحالة
            }));
        }

        // الحصول على حالة الجهاز
        function getDeviceStatus(deviceId) {
            const device = systemData.devices.find(d => d.id === deviceId);
            if (!device) return 'offline';

            const license = systemData.licenses.find(l => l.deviceId === deviceId);
            if (!license) return 'offline';

            if (!license.isActive) return 'suspended';
            if (new Date(license.expiresAt) < new Date()) return 'expired';

            return Math.random() > 0.3 ? 'online' : 'offline';
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalLicenses = systemData.licenses.length;
            const totalDevices = systemData.licenses.filter(l => l.deviceId).length;
            const activeUsers = systemData.users.filter(u => u.status === 'online').length;
            const expiredLicenses = systemData.licenses.filter(l => new Date(l.expiresAt) < new Date()).length;

            document.getElementById('totalLicenses').textContent = totalLicenses;
            document.getElementById('totalDevices').textContent = totalDevices;
            document.getElementById('activeUsers').textContent = activeUsers;
            document.getElementById('expiredLicenses').textContent = expiredLicenses;
        }

        // معالجة توليد التراخيص
        async function handleLicenseGeneration(event) {
            event.preventDefault();

            const licenseType = document.getElementById('licenseType').value;
            const licenseCount = parseInt(document.getElementById('licenseCount').value);
            const licenseNotes = document.getElementById('licenseNotes').value;

            if (licenseCount < 1 || licenseCount > 100) {
                showNotification('عدد التراخيص يجب أن يكون بين 1 و 100', 'error');
                return;
            }

            const spinner = document.getElementById('generateSpinner');
            const submitBtn = event.target.querySelector('button[type="submit"]');

            spinner.style.display = 'inline-block';
            submitBtn.disabled = true;

            try {
                await new Promise(resolve => setTimeout(resolve, 1500));

                const generatedLicenses = [];

                for (let i = 0; i < licenseCount; i++) {
                    const license = generateNewLicense(licenseType, licenseNotes);
                    generatedLicenses.push(license);
                    systemData.licenses.push(license);
                }

                // حفظ التراخيص
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));

                // عرض التراخيص المولدة
                displayGeneratedLicenses(generatedLicenses);

                // تحديث الإحصائيات
                updateStatistics();

                showNotification(`تم توليد ${licenseCount} ترخيص بنجاح`, 'success');

            } catch (error) {
                showNotification('حدث خطأ في توليد التراخيص', 'error');
            } finally {
                spinner.style.display = 'none';
                submitBtn.disabled = false;
            }
        }

        // توليد ترخيص جديد
        function generateNewLicense(type, notes) {
            const now = new Date();
            let expiresAt = new Date();

            switch (type) {
                case 'demo':
                    expiresAt.setDate(now.getDate() + 30);
                    break;
                case 'monthly':
                    expiresAt.setMonth(now.getMonth() + 1);
                    break;
                case 'yearly':
                    expiresAt.setFullYear(now.getFullYear() + 1);
                    break;
                case 'lifetime':
                    expiresAt.setFullYear(now.getFullYear() + 100);
                    break;
            }

            return {
                code: generateLicenseCode(),
                type: type,
                expiresAt: expiresAt.toISOString(),
                isActive: true,
                deviceId: null,
                activatedAt: null,
                createdAt: now.toISOString(),
                notes: notes || ''
            };
        }

        // توليد كود ترخيص
        function generateLicenseCode() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';

            for (let i = 0; i < 4; i++) {
                if (i > 0) result += '-';
                for (let j = 0; j < 4; j++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
            }

            return result;
        }

        // عرض التراخيص المولدة
        function displayGeneratedLicenses(licenses) {
            const container = document.getElementById('generatedLicenses');
            const listContainer = document.getElementById('licensesList');

            listContainer.innerHTML = '';

            licenses.forEach(license => {
                const item = document.createElement('div');
                item.style.cssText = 'background: rgba(52, 152, 219, 0.1); padding: 0.5rem; margin: 0.25rem 0; border-radius: 4px; font-family: monospace; font-size: 0.8rem; display: flex; justify-content: space-between; align-items: center;';
                item.innerHTML = `
                    <span>${license.code}</span>
                    <button onclick="copyToClipboard('${license.code}')" style="background: #3498db; color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer; font-size: 0.7rem;">
                        <i class="fas fa-copy"></i>
                    </button>
                `;
                listContainer.appendChild(item);
            });

            container.style.display = 'block';
        }

        // وظائف التحكم عن بُعد
        function remoteActivate() {
            const deviceId = document.getElementById('targetDeviceId').value.trim();
            if (!deviceId) {
                showNotification('يرجى إدخال معرف الجهاز', 'warning');
                return;
            }

            // محاكاة التفعيل عن بُعد
            const license = systemData.licenses.find(l => l.deviceId === deviceId);
            if (license) {
                license.isActive = true;
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
                showNotification(`تم تفعيل الجهاز: ${deviceId}`, 'success');
                loadUsersTable();
            } else {
                showNotification('الجهاز غير موجود', 'error');
            }
        }

        function remoteSuspend() {
            const deviceId = document.getElementById('targetDeviceId').value.trim();
            if (!deviceId) {
                showNotification('يرجى إدخال معرف الجهاز', 'warning');
                return;
            }

            const license = systemData.licenses.find(l => l.deviceId === deviceId);
            if (license) {
                license.isActive = false;
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
                showNotification(`تم إيقاف الجهاز مؤقتاً: ${deviceId}`, 'warning');
                loadUsersTable();
            } else {
                showNotification('الجهاز غير موجود', 'error');
            }
        }

        function remoteShutdown() {
            const deviceId = document.getElementById('targetDeviceId').value.trim();
            if (!deviceId) {
                showNotification('يرجى إدخال معرف الجهاز', 'warning');
                return;
            }

            if (!confirm(`هل أنت متأكد من إيقاف الجهاز نهائياً: ${deviceId}؟`)) {
                return;
            }

            const license = systemData.licenses.find(l => l.deviceId === deviceId);
            if (license) {
                license.isActive = false;
                license.deviceId = null;
                license.activatedAt = null;
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
                showNotification(`تم إيقاف الجهاز نهائياً: ${deviceId}`, 'error');
                loadUsersTable();
            } else {
                showNotification('الجهاز غير موجود', 'error');
            }
        }

        function getDeviceInfo() {
            const deviceId = document.getElementById('targetDeviceId').value.trim();
            if (!deviceId) {
                showNotification('يرجى إدخال معرف الجهاز', 'warning');
                return;
            }

            const license = systemData.licenses.find(l => l.deviceId === deviceId);
            if (license) {
                const info = `
معرف الجهاز: ${deviceId}
كود الترخيص: ${license.code}
نوع الترخيص: ${getLicenseTypeName(license.type)}
الحالة: ${license.isActive ? 'نشط' : 'معطل'}
تاريخ التفعيل: ${license.activatedAt ? new Date(license.activatedAt).toLocaleDateString('ar-SA') : 'غير محدد'}
تاريخ الانتهاء: ${new Date(license.expiresAt).toLocaleDateString('ar-SA')}
                `;
                alert(info);
            } else {
                showNotification('الجهاز غير موجود', 'error');
            }
        }

        function deleteDevice() {
            const deviceId = document.getElementById('targetDeviceId').value.trim();
            if (!deviceId) {
                showNotification('يرجى إدخال معرف الجهاز', 'warning');
                return;
            }

            if (!confirm(`هل أنت متأكد من حذف الجهاز: ${deviceId}؟`)) {
                return;
            }

            const licenseIndex = systemData.licenses.findIndex(l => l.deviceId === deviceId);
            if (licenseIndex !== -1) {
                systemData.licenses[licenseIndex].deviceId = null;
                systemData.licenses[licenseIndex].activatedAt = null;
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
                showNotification(`تم حذف الجهاز: ${deviceId}`, 'success');
                loadUsersTable();
                document.getElementById('targetDeviceId').value = '';
            } else {
                showNotification('الجهاز غير موجود', 'error');
            }
        }

        // تحديث بيانات المستخدمين
        function refreshUserData() {
            loadSystemData();
            updateActiveUsers();
            loadUsersTable();
            updateStatistics();
            showNotification('تم تحديث بيانات المستخدمين', 'success');
        }

        // البحث عن المستخدمين
        function searchUsers() {
            const searchTerm = document.getElementById('userSearch').value.toLowerCase();
            const filteredUsers = systemData.users.filter(user =>
                user.deviceId.toLowerCase().includes(searchTerm) ||
                user.licenseCode.toLowerCase().includes(searchTerm)
            );
            loadUsersTable(filteredUsers);
        }

        // تحميل جدول المستخدمين
        function loadUsersTable(users = null) {
            const usersToShow = users || systemData.users;
            const tbody = document.getElementById('usersTableBody');

            if (usersToShow.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 2rem; opacity: 0.6;">
                            لا توجد بيانات متاحة
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';

            usersToShow.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="font-family: monospace; font-size: 0.8rem;">${user.deviceId}</td>
                    <td style="font-family: monospace; font-size: 0.8rem;">${user.licenseCode}</td>
                    <td>${getLicenseTypeName(user.licenseType)}</td>
                    <td><span class="status-badge status-${user.status}">${getStatusName(user.status)}</span></td>
                    <td>${new Date(user.lastActivity).toLocaleDateString('ar-SA')}</td>
                    <td>${new Date(user.expiresAt).toLocaleDateString('ar-SA')}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn activate" onclick="activateUser('${user.deviceId}')" title="تفعيل">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="action-btn suspend" onclick="suspendUser('${user.deviceId}')" title="إيقاف">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button class="action-btn delete" onclick="deleteUser('${user.deviceId}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button class="action-btn info" onclick="showUserInfo('${user.deviceId}')" title="معلومات">
                                <i class="fas fa-info"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // وظائف إدارة المستخدمين
        function activateUser(deviceId) {
            document.getElementById('targetDeviceId').value = deviceId;
            remoteActivate();
        }

        function suspendUser(deviceId) {
            document.getElementById('targetDeviceId').value = deviceId;
            remoteSuspend();
        }

        function deleteUser(deviceId) {
            document.getElementById('targetDeviceId').value = deviceId;
            deleteDevice();
        }

        function showUserInfo(deviceId) {
            document.getElementById('targetDeviceId').value = deviceId;
            getDeviceInfo();
        }

        // وظائف النسخ الاحتياطي
        function createBackup() {
            const backupData = {
                timestamp: new Date().toISOString(),
                version: '2.2.0',
                data: {
                    licenses: systemData.licenses,
                    devices: systemData.devices,
                    sessions: systemData.sessions
                }
            };

            const dataStr = JSON.stringify(backupData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `admin_backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم إنشاء النسخة الاحتياطية', 'success');
        }

        function restoreBackup(input) {
            const file = input.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const backupData = JSON.parse(e.target.result);

                    if (backupData.data) {
                        systemData.licenses = backupData.data.licenses || [];
                        systemData.devices = backupData.data.devices || [];
                        systemData.sessions = backupData.data.sessions || [];

                        localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));
                        localStorage.setItem('devices', JSON.stringify(systemData.devices));
                        localStorage.setItem('activeSessions', JSON.stringify(systemData.sessions));

                        refreshUserData();
                        showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                    } else {
                        showNotification('ملف النسخة الاحتياطية غير صالح', 'error');
                    }
                } catch (error) {
                    showNotification('خطأ في قراءة ملف النسخة الاحتياطية', 'error');
                }
            };
            reader.readAsText(file);
        }

        function clearAllData() {
            if (!confirm('تحذير: سيتم حذف جميع البيانات نهائياً. هل تريد المتابعة؟')) {
                return;
            }

            if (!confirm('هذا الإجراء لا يمكن التراجع عنه. هل أنت متأكد؟')) {
                return;
            }

            systemData = { licenses: [], devices: [], users: [], sessions: [] };
            localStorage.removeItem('validLicenses');
            localStorage.removeItem('devices');
            localStorage.removeItem('activeSessions');

            addDefaultLicenses();
            refreshUserData();
            showNotification('تم مسح جميع البيانات', 'warning');
        }

        // تحديث مدة الترخيص حسب النوع
        function updateLicenseDuration() {
            const licenseType = document.getElementById('customerLicenseType').value;
            const durationInput = document.getElementById('customerLicenseDuration');

            switch(licenseType) {
                case 'demo':
                    durationInput.value = 30;
                    break;
                case 'monthly':
                    durationInput.value = 30;
                    break;
                case 'yearly':
                    durationInput.value = 365;
                    break;
                case 'lifetime':
                    durationInput.value = 3650; // 10 سنوات
                    break;
                case 'admin':
                    durationInput.value = 365;
                    break;
            }
        }

        // تفعيل العميل مباشرة من جهاز المطور
        function activateCustomerDirectly() {
            const deviceId = document.getElementById('customerDeviceId').value.trim();
            const licenseType = document.getElementById('customerLicenseType').value;
            const duration = parseInt(document.getElementById('customerLicenseDuration').value);
            const customerName = document.getElementById('customerName').value.trim();
            const notes = document.getElementById('customerNotes').value.trim();
            const resultDiv = document.getElementById('activationResult');

            // التحقق من البيانات المطلوبة
            if (!deviceId) {
                resultDiv.innerHTML = '<div style="background: #ffebee; color: #c62828; padding: 10px; border-radius: 5px; border-left: 4px solid #f44336;">❌ يرجى إدخال معرف جهاز العميل</div>';
                return;
            }

            // التحقق من صحة تنسيق معرف الجهاز (مرن لجميع الأنواع)
            if (!isValidDeviceIdFormat(deviceId)) {
                resultDiv.innerHTML = '<div style="background: #ffebee; color: #c62828; padding: 10px; border-radius: 5px; border-left: 4px solid #f44336;">❌ تنسيق معرف الجهاز غير صالح. أمثلة صحيحة:<br>• WIN-CHR-4F27B665-2025<br>• DEV-GEN-ABCD1234-2024<br>• MAC-SAF-A1B2C3D4-2024</div>';
                return;
            }

            if (duration < 1 || duration > 3650) {
                resultDiv.innerHTML = '<div style="background: #ffebee; color: #c62828; padding: 10px; border-radius: 5px; border-left: 4px solid #f44336;">❌ مدة الترخيص يجب أن تكون بين 1 و 3650 يوم</div>';
                return;
            }

            try {
                // توليد كود ترخيص جديد
                const licenseCode = generateLicenseCode(licenseType);

                // إنشاء الترخيص الجديد
                const newLicense = {
                    id: 'LIC-' + Date.now(),
                    code: licenseCode,
                    type: licenseType,
                    status: 'active',
                    expiresAt: new Date(Date.now() + duration * 24 * 60 * 60 * 1000).toISOString(),
                    isActive: true,
                    deviceId: deviceId, // ربط مباشر بالجهاز
                    activatedAt: new Date().toISOString(),
                    createdAt: new Date().toISOString(),
                    notes: notes || `تم التفعيل مباشرة من لوحة التحكم${customerName ? ' للعميل: ' + customerName : ''}`,
                    maxDevices: licenseType === 'admin' ? 5 : 1,
                    features: licenseType === 'demo' ? ['basic', 'reports'] : ['all'],
                    createdBy: 'admin-remote',
                    customerName: customerName,
                    activationMethod: 'remote-direct',
                    lastModified: new Date().toISOString()
                };

                // إضافة الترخيص لقاعدة البيانات
                systemData.licenses.push(newLicense);
                localStorage.setItem('validLicenses', JSON.stringify(systemData.licenses));

                // تسجيل الجهاز
                const existingDevice = systemData.devices.find(d => d.deviceId === deviceId);
                if (!existingDevice) {
                    const newDevice = {
                        id: 'DEV-' + Date.now(),
                        deviceId: deviceId,
                        licenseCode: licenseCode,
                        userAgent: 'Remote Activation',
                        firstSeen: new Date().toISOString(),
                        lastSeen: new Date().toISOString(),
                        status: 'active',
                        loginCount: 0,
                        location: 'Remote',
                        notes: `تم التسجيل عن بُعد${customerName ? ' للعميل: ' + customerName : ''}`,
                        customerName: customerName,
                        activationMethod: 'remote-direct'
                    };

                    systemData.devices.push(newDevice);
                    localStorage.setItem('devices', JSON.stringify(systemData.devices));
                }

                // تحديث الإحصائيات
                updateStatistics();
                refreshUserData();

                // عرض النتيجة
                const licenseTypeName = {
                    'demo': 'تجريبي (30 يوم)',
                    'monthly': 'شهري',
                    'yearly': 'سنوي',
                    'lifetime': 'مدى الحياة',
                    'admin': 'إداري'
                }[licenseType];

                const expiryDate = new Date(newLicense.expiresAt).toLocaleDateString('ar-SA');

                resultDiv.innerHTML = `
                    <div style="background: #e8f5e8; color: #2e7d32; padding: 15px; border-radius: 8px; border-left: 4px solid #4caf50;">
                        <h4 style="margin: 0 0 10px 0;">✅ تم تفعيل العميل بنجاح!</h4>
                        <div style="font-family: monospace; background: rgba(255,255,255,0.7); padding: 10px; border-radius: 5px; margin: 10px 0;">
                            <strong>كود الترخيص:</strong> <span style="font-size: 1.2rem; color: #1976d2; font-weight: bold;">${licenseCode}</span>
                        </div>
                        <p><strong>نوع الترخيص:</strong> ${licenseTypeName}</p>
                        <p><strong>معرف الجهاز:</strong> ${deviceId}</p>
                        <p><strong>تاريخ الانتهاء:</strong> ${expiryDate}</p>
                        ${customerName ? `<p><strong>اسم العميل:</strong> ${customerName}</p>` : ''}
                        <div style="background: #fff3cd; color: #856404; padding: 10px; border-radius: 5px; margin-top: 10px;">
                            <strong>📱 تعليمات للعميل:</strong><br>
                            1. أرسل كود الترخيص أعلاه للعميل<br>
                            2. اطلب منه إدخاله في صفحة تسجيل الدخول<br>
                            3. سيتم تفعيل التطبيق تلقائياً
                        </div>
                        <div style="margin-top: 10px;">
                            <button onclick="copyLicenseCode('${licenseCode}')" style="background: #2196f3; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">
                                <i class="fas fa-copy"></i> نسخ كود الترخيص
                            </button>
                            <button onclick="sendLicenseToCustomer('${licenseCode}', '${customerName}', '${deviceId}')" style="background: #4caf50; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                                <i class="fas fa-share"></i> إرسال للعميل
                            </button>
                        </div>
                    </div>
                `;

                // تنظيف النموذج
                document.getElementById('customerDeviceId').value = '';
                document.getElementById('customerName').value = '';
                document.getElementById('customerNotes').value = '';

                showNotification(`تم تفعيل العميل بنجاح - كود الترخيص: ${licenseCode}`, 'success');

            } catch (error) {
                resultDiv.innerHTML = `<div style="background: #ffebee; color: #c62828; padding: 10px; border-radius: 5px; border-left: 4px solid #f44336;">❌ خطأ في التفعيل: ${error.message}</div>`;
                console.error('خطأ في التفعيل المباشر:', error);
            }
        }

        // التحقق من صحة تنسيق معرف الجهاز
        function isValidDeviceIdFormat(deviceId) {
            // قبول تنسيقات متعددة
            const patterns = [
                /^[A-Z]{3}-[A-Z]{3}-[A-Z0-9]{8}-[0-9]{4}$/,  // WIN-CHR-4F27B665-2025
                /^DEV-[A-Z0-9]{3,}-[A-Z0-9]{4,}-[A-Z0-9]{4,}$/,  // DEV-GEN-XXXXX-XXXX
                /^[A-Z0-9]{3,}-[A-Z0-9]{3,}-[A-Z0-9]{4,}-[A-Z0-9]{4,}$/,  // عام
                /^[A-Z0-9\-]{10,}$/  // أي معرف يحتوي على أحرف وأرقام وشرطات بطول 10+ أحرف
            ];

            return patterns.some(pattern => pattern.test(deviceId.toUpperCase()));
        }

        // توليد كود ترخيص جديد
        function generateLicenseCode(type) {
            const prefix = {
                'demo': 'DEMO',
                'monthly': 'MNTH',
                'yearly': 'YEAR',
                'lifetime': 'LIFE',
                'admin': 'ADMN'
            }[type] || 'CUST';

            const year = new Date().getFullYear();
            const timestamp = Date.now().toString(36).toUpperCase();
            const random = Math.random().toString(36).substr(2, 4).toUpperCase();

            return `${prefix}-${year}-${timestamp}-${random}`;
        }

        // نسخ كود الترخيص
        function copyLicenseCode(licenseCode) {
            navigator.clipboard.writeText(licenseCode).then(() => {
                showNotification('تم نسخ كود الترخيص', 'success');
            }).catch(() => {
                // طريقة بديلة للنسخ
                const textArea = document.createElement('textarea');
                textArea.value = licenseCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ كود الترخيص', 'success');
            });
        }

        // إرسال الترخيص للعميل
        function sendLicenseToCustomer(licenseCode, customerName, deviceId) {
            const message = `
مرحباً ${customerName ? customerName : 'عزيزي العميل'},

تم تفعيل ترخيص نظام مؤسسة وقود المستقبل بنجاح!

🔑 كود الترخيص: ${licenseCode}
📱 معرف الجهاز: ${deviceId}

📋 تعليمات التفعيل:
1. افتح تطبيق مؤسسة وقود المستقبل
2. أدخل كود الترخيص في الحقل المخصص
3. اضغط "تسجيل الدخول"
4. سيتم تفعيل التطبيق تلقائياً

📞 للدعم الفني:
- الهاتف: +966-11-123-4567
- البريد: <EMAIL>

شكراً لثقتكم بنا!
فريق مؤسسة وقود المستقبل
            `.trim();

            // فتح تطبيق البريد الإلكتروني أو الواتساب
            const emailSubject = encodeURIComponent('كود ترخيص نظام مؤسسة وقود المستقبل');
            const emailBody = encodeURIComponent(message);
            const emailUrl = `mailto:?subject=${emailSubject}&body=${emailBody}`;

            // عرض خيارات الإرسال
            const sendOptions = `
                <div style="background: #f5f5f5; padding: 15px; border-radius: 8px; margin-top: 10px;">
                    <h4>📤 خيارات الإرسال:</h4>
                    <button onclick="window.open('${emailUrl}')" style="background: #ff9800; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin: 5px;">
                        <i class="fas fa-envelope"></i> إرسال بالبريد الإلكتروني
                    </button>
                    <button onclick="copyMessageToClipboard(\`${message.replace(/`/g, '\\`')}\`)" style="background: #9c27b0; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin: 5px;">
                        <i class="fas fa-copy"></i> نسخ الرسالة
                    </button>
                    <button onclick="openWhatsApp(\`${message.replace(/`/g, '\\`')}\`)" style="background: #4caf50; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin: 5px;">
                        <i class="fab fa-whatsapp"></i> إرسال بالواتساب
                    </button>
                </div>
            `;

            document.getElementById('activationResult').innerHTML += sendOptions;
        }

        // نسخ الرسالة للحافظة
        function copyMessageToClipboard(message) {
            navigator.clipboard.writeText(message).then(() => {
                showNotification('تم نسخ الرسالة - يمكنك لصقها في أي تطبيق', 'success');
            });
        }

        // فتح الواتساب
        function openWhatsApp(message) {
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        // وظائف مساعدة
        function getLicenseTypeName(type) {
            const names = {
                'demo': 'تجريبي',
                'monthly': 'شهري',
                'yearly': 'سنوي',
                'lifetime': 'مدى الحياة'
            };
            return names[type] || type;
        }

        function getStatusName(status) {
            const names = {
                'online': 'متصل',
                'offline': 'غير متصل',
                'suspended': 'معلق',
                'expired': 'منتهي'
            };
            return names[status] || status;
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('تم نسخ النص', 'success');
            }).catch(() => {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('تم نسخ النص', 'success');
            });
        }

        // نظام الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 400);
            }, 4000);
        }
    </script>
</body>
</html>
