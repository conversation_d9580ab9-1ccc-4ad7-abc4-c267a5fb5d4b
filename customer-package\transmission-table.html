<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جدول الإرسال - مركز وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            background-color: #f1f5f9;
            margin: 0;
            padding: 20px;
            direction: rtl;
            color: #334155;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            margin: 0 0 0.5rem 0;
            font-size: 1.8rem;
            font-weight: bold;
        }

        .header h2 {
            margin: 0 0 1rem 0;
            font-size: 1.4rem;
            font-weight: 600;
            opacity: 0.9;
        }

        .header p {
            margin: 0.3rem 0;
            font-size: 1.1rem;
            opacity: 0.8;
        }

        .header .highlight {
            font-weight: bold;
            font-size: 1.2rem;
            margin: 1rem 0;
        }

        .controls {
            padding: 1.5rem;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .controls-left {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .controls-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .btn {
            padding: 0.75rem 1.25rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-info {
            background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
            color: white;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #0e7490 0%, #0891b2 100%);
        }

        .search-box {
            display: flex;
            align-items: center;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }

        .search-box input {
            border: none;
            padding: 0.75rem 1rem;
            outline: none;
            width: 250px;
            font-size: 0.9rem;
        }

        .search-box button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1rem;
            cursor: pointer;
        }

        .filters {
            padding: 1rem 1.5rem;
            background: white;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filter-group label {
            font-weight: 500;
            color: #374151;
        }

        .filter-group select {
            padding: 0.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            min-width: 120px;
        }

        .table-container {
            padding: 1.5rem;
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        th {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 1rem 0.75rem;
            text-align: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        td {
            padding: 0.875rem 0.75rem;
            text-align: center;
            border-bottom: 1px solid #f1f5f9;
            font-size: 0.9rem;
        }

        tbody tr:nth-child(even) {
            background-color: #f8fafc;
        }

        tbody tr:hover {
            background-color: #f1f5f9;
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .action-buttons {
            display: flex;
            gap: 0.25rem;
            justify-content: center;
        }

        .btn-sm {
            padding: 0.5rem;
            font-size: 0.8rem;
            min-width: auto;
        }

        .summary {
            padding: 1.5rem;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .summary-card {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .summary-card h4 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
            opacity: 0.9;
        }

        .summary-card .count {
            font-size: 2rem;
            font-weight: bold;
            margin: 0;
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* تنسيقات الطباعة */
        @media print {
            body { margin: 0; padding: 0; }
            .controls, .filters, .summary { display: none !important; }
            .container { box-shadow: none; border-radius: 0; }
            .header { background: white !important; color: black !important; border: 2px solid black; }
            th { background: #f0f0f0 !important; color: black !important; }
            td, th { border: 1px solid black !important; padding: 8px 4px !important; font-size: 12px !important; }
            table { page-break-inside: auto; }
            tr { page-break-inside: avoid; page-break-after: auto; }
            thead { display: table-header-group; }
        }

        /* تنسيقات الشاشات الصغيرة */
        @media (max-width: 768px) {
            .controls { flex-direction: column; align-items: stretch; }
            .controls-left, .controls-right { justify-content: center; }
            .search-box input { width: 200px; }
            .filters { flex-direction: column; gap: 1rem; }
            .summary { grid-template-columns: repeat(2, 1fr); }
            table { font-size: 0.8rem; }
            th, td { padding: 0.5rem 0.25rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- رأس الصفحة -->
        <div class="header">
            <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
            <h2>مركز وقود المستقبل - عزيري عبد الله اسحاق</h2>
            <p>رقم: 463/2019</p>
            <p>إلى السيد: مدير الصناعة و المناجم لولاية المدية</p>
            <p class="highlight"><strong>جدول إرسال</strong></p>
            <p>تجدون طي هذه المراسلة الوثائق الخاصة بالسيارات المجهزة بغاز البترول المميع شهر <span id="current-month-year"></span>.</p>
        </div>

        <!-- أدوات التحكم -->
        <div class="controls">
            <div class="controls-left">
                <button type="button" class="btn btn-info" onclick="goBackToMainSystem()">
                    <i class="fas fa-arrow-right"></i> العودة للنظام الرئيسي
                </button>
                <button type="button" class="btn btn-primary" onclick="showAddEntryModal()">
                    <i class="fas fa-plus"></i> إضافة عملية جديدة
                </button>
                <button type="button" class="btn btn-secondary" onclick="printTable()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button type="button" class="btn btn-secondary" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>
                <button type="button" class="btn btn-danger" onclick="clearTable()">
                    <i class="fas fa-trash"></i> مسح الجدول
                </button>
            </div>
            <div class="controls-right">
                <div class="search-box">
                    <input type="text" id="search-input" placeholder="بحث في الجدول...">
                    <button type="button" onclick="searchTable()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- فلاتر -->
        <div class="filters">
            <div class="filter-group">
                <label for="type-filter">نوع العملية:</label>
                <select id="type-filter" onchange="filterTable()">
                    <option value="all">الكل</option>
                    <option value="تركيب">تركيب</option>
                    <option value="مراقبة">مراقبة</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="month-filter">الشهر:</label>
                <select id="month-filter" onchange="filterTable()">
                    <option value="all">الكل</option>
                    <option value="current">الشهر الحالي</option>
                    <option value="last">الشهر الماضي</option>
                </select>
            </div>
        </div>

        <!-- الجدول -->
        <div class="table-container">
            <table id="transmission-table">
                <thead>
                    <tr>
                        <th>تركيب أو مراقبة</th>
                        <th>رقم خزان الغاز</th>
                        <th>الصنف</th>
                        <th>الرقم التسلسلي</th>
                        <th>رقم التسجيل</th>
                        <th>الإسم و اللقب</th>
                        <th>الرقم</th>
                        <th>تاريخ العملية</th>
                        <th class="no-print">الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="table-body">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </tbody>
            </table>
            
            <div id="empty-state" class="empty-state" style="display: none;">
                <i class="fas fa-table"></i>
                <h3>لا توجد بيانات</h3>
                <p>لم يتم إضافة أي عمليات إلى جدول الإرسال بعد</p>
                <button type="button" class="btn btn-primary" onclick="showAddEntryModal()">
                    <i class="fas fa-plus"></i> إضافة أول عملية
                </button>
            </div>
        </div>

        <!-- ملخص الإحصائيات -->
        <div class="summary">
            <div class="summary-card">
                <h4>إجمالي العمليات</h4>
                <div class="count" id="total-count">0</div>
            </div>
            <div class="summary-card">
                <h4>عمليات التركيب</h4>
                <div class="count" id="installation-count">0</div>
            </div>
            <div class="summary-card">
                <h4>عمليات المراقبة</h4>
                <div class="count" id="monitoring-count">0</div>
            </div>

            <div class="summary-card">
                <h4>عمليات هذا الشهر</h4>
                <div class="count" id="current-month-count">0</div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل عملية -->
    <div id="entry-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 12px; width: 90%; max-width: 500px;">
            <h3 id="modal-title">إضافة عملية جديدة</h3>
            <form id="entry-form">
                <div style="margin-bottom: 1rem;">
                    <label>نوع العملية:</label>
                    <select id="entry-type" required style="width: 100%; padding: 0.5rem; margin-top: 0.25rem; border: 1px solid #e2e8f0; border-radius: 6px;">
                        <option value="تركيب">تركيب</option>
                        <option value="مراقبة">مراقبة</option>
                    </select>
                </div>
                <div style="margin-bottom: 1rem;">
                    <label>رقم خزان الغاز:</label>
                    <input type="text" id="entry-tank-number" required style="width: 100%; padding: 0.5rem; margin-top: 0.25rem; border: 1px solid #e2e8f0; border-radius: 6px;">
                </div>
                <div style="margin-bottom: 1rem;">
                    <label>الصنف:</label>
                    <input type="text" id="entry-car-type" required style="width: 100%; padding: 0.5rem; margin-top: 0.25rem; border: 1px solid #e2e8f0; border-radius: 6px;">
                </div>
                <div style="margin-bottom: 1rem;">
                    <label>الرقم التسلسلي:</label>
                    <input type="text" id="entry-serial-number" required style="width: 100%; padding: 0.5rem; margin-top: 0.25rem; border: 1px solid #e2e8f0; border-radius: 6px;">
                </div>
                <div style="margin-bottom: 1rem;">
                    <label>رقم التسجيل:</label>
                    <input type="text" id="entry-registration-number" required style="width: 100%; padding: 0.5rem; margin-top: 0.25rem; border: 1px solid #e2e8f0; border-radius: 6px;">
                </div>
                <div style="margin-bottom: 1rem;">
                    <label>الإسم واللقب:</label>
                    <input type="text" id="entry-owner-name" required style="width: 100%; padding: 0.5rem; margin-top: 0.25rem; border: 1px solid #e2e8f0; border-radius: 6px;">
                </div>
                <div style="margin-bottom: 1rem;">
                    <label>تاريخ العملية:</label>
                    <input type="date" id="entry-operation-date" required style="width: 100%; padding: 0.5rem; margin-top: 0.25rem; border: 1px solid #e2e8f0; border-radius: 6px;">
                </div>
                <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script>
        // تحميل البيانات من localStorage أو من التطبيق الرئيسي
        function loadTransmissionData() {
            try {
                // محاولة تحميل من transmissionTableData أولاً
                let savedData = localStorage.getItem('transmissionTableData');
                if (savedData) {
                    return JSON.parse(savedData);
                }

                // محاولة تحميل من البيانات الرئيسية
                const mainData = localStorage.getItem('gasShopData');
                if (mainData) {
                    const parsedData = JSON.parse(mainData);
                    return parsedData.transmissionTable || [];
                }

                return [];
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                return [];
            }
        }

        // تحميل إعدادات جدول الإرسال من config.json
        function loadTransmissionConfig() {
            try {
                const configData = localStorage.getItem('appConfig');
                if (configData) {
                    const config = JSON.parse(configData);
                    return config.transmissionTable || {};
                }
                return {};
            } catch (error) {
                console.error('خطأ في تحميل إعدادات جدول الإرسال:', error);
                return {};
            }
        }

        // بيانات جدول الإرسال
        let transmissionData = loadTransmissionData();
        let editingIndex = -1;

        // تحديث الشهر والسنة الحالية
        function updateCurrentMonthYear() {
            const now = new Date();
            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            const currentMonthYear = `${monthNames[now.getMonth()]} ${now.getFullYear()}`;
            document.getElementById('current-month-year').textContent = currentMonthYear;
        }

        // حفظ البيانات
        function saveData() {
            try {
                // حفظ في transmissionTableData
                localStorage.setItem('transmissionTableData', JSON.stringify(transmissionData));

                // تحديث البيانات في النظام الرئيسي أيضاً
                const mainData = localStorage.getItem('gasShopData');
                if (mainData) {
                    const parsedData = JSON.parse(mainData);
                    parsedData.transmissionTable = transmissionData;
                    localStorage.setItem('gasShopData', JSON.stringify(parsedData));
                }

                // إشعار النافذة الأصلية بالتحديث (إذا كانت مفتوحة)
                if (window.opener && !window.opener.closed) {
                    try {
                        window.opener.postMessage({
                            type: 'transmissionTableUpdate',
                            data: transmissionData
                        }, '*');
                    } catch (e) {
                        console.log('تعذر إرسال التحديث للنافذة الأصلية');
                    }
                }

            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                showToast('خطأ في حفظ البيانات', false);
            }
        }

        // تحديث الجدول
        function updateTable() {
            const tableBody = document.getElementById('table-body');
            const emptyState = document.getElementById('empty-state');
            
            // تطبيق الفلاتر
            let filteredData = [...transmissionData];
            
            // فلتر النوع
            const typeFilter = document.getElementById('type-filter').value;
            if (typeFilter !== 'all') {
                filteredData = filteredData.filter(entry => entry.type === typeFilter);
            }
            
            // فلتر الشهر
            const monthFilter = document.getElementById('month-filter').value;
            if (monthFilter !== 'all') {
                const now = new Date();
                const currentMonth = now.getMonth();
                const currentYear = now.getFullYear();
                
                if (monthFilter === 'current') {
                    filteredData = filteredData.filter(entry => {
                        const entryDate = new Date(entry.operationDate);
                        return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
                    });
                } else if (monthFilter === 'last') {
                    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
                    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
                    filteredData = filteredData.filter(entry => {
                        const entryDate = new Date(entry.operationDate);
                        return entryDate.getMonth() === lastMonth && entryDate.getFullYear() === lastMonthYear;
                    });
                }
            }
            
            // فلتر البحث
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            if (searchTerm) {
                filteredData = filteredData.filter(entry =>
                    entry.ownerName.toLowerCase().includes(searchTerm) ||
                    entry.registrationNumber.toLowerCase().includes(searchTerm) ||
                    entry.tankNumber.toLowerCase().includes(searchTerm) ||
                    entry.carType.toLowerCase().includes(searchTerm)
                );
            }
            
            if (filteredData.length === 0) {
                tableBody.innerHTML = '';
                emptyState.style.display = 'block';
            } else {
                emptyState.style.display = 'none';
                
                // ترتيب البيانات حسب تاريخ العملية (الأحدث أولاً)
                filteredData.sort((a, b) => new Date(b.operationDate) - new Date(a.operationDate));
                
                tableBody.innerHTML = filteredData.map((entry, index) => `
                    <tr>
                        <td>${entry.type}</td>
                        <td>${entry.tankNumber}</td>
                        <td>${entry.carType}</td>
                        <td>${entry.serialNumber}</td>
                        <td>${entry.registrationNumber}</td>
                        <td>${entry.ownerName}</td>
                        <td>${index + 1}</td>
                        <td>${formatDate(entry.operationDate)}</td>
                        <td class="no-print">
                            <div class="action-buttons">
                                <button type="button" class="btn btn-sm btn-secondary" onclick="editEntry(${transmissionData.indexOf(entry)})" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteEntry(${transmissionData.indexOf(entry)})" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `).join('');
            }
            
            updateSummary();
        }

        // تحديث الملخص
        function updateSummary() {
            const totalCount = transmissionData.length;
            const installationCount = transmissionData.filter(entry => entry.type === 'تركيب').length;
            const monitoringCount = transmissionData.filter(entry => entry.type === 'مراقبة').length;

            // عمليات الشهر الحالي
            const now = new Date();
            const currentMonth = now.getMonth();
            const currentYear = now.getFullYear();
            const currentMonthCount = transmissionData.filter(entry => {
                const entryDate = new Date(entry.operationDate);
                return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
            }).length;

            document.getElementById('total-count').textContent = totalCount;
            document.getElementById('installation-count').textContent = installationCount;
            document.getElementById('monitoring-count').textContent = monitoringCount;
            document.getElementById('current-month-count').textContent = currentMonthCount;
        }

        // تنسيق التاريخ
        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }

        // عرض نافذة إضافة عملية
        function showAddEntryModal() {
            editingIndex = -1;
            document.getElementById('modal-title').textContent = 'إضافة عملية جديدة';
            document.getElementById('entry-form').reset();
            document.getElementById('entry-operation-date').value = new Date().toISOString().split('T')[0];
            document.getElementById('entry-modal').style.display = 'block';
        }

        // تعديل عملية
        function editEntry(index) {
            editingIndex = index;
            const entry = transmissionData[index];

            document.getElementById('modal-title').textContent = 'تعديل العملية';
            document.getElementById('entry-type').value = entry.type;
            document.getElementById('entry-tank-number').value = entry.tankNumber;
            document.getElementById('entry-car-type').value = entry.carType;
            document.getElementById('entry-serial-number').value = entry.serialNumber;
            document.getElementById('entry-registration-number').value = entry.registrationNumber;
            document.getElementById('entry-owner-name').value = entry.ownerName;
            document.getElementById('entry-operation-date').value = entry.operationDate;

            document.getElementById('entry-modal').style.display = 'block';
        }

        // حذف عملية
        function deleteEntry(index) {
            if (confirm('هل أنت متأكد من حذف هذه العملية؟')) {
                transmissionData.splice(index, 1);
                saveData();
                updateTable();
                showToast('تم حذف العملية بنجاح');
            }
        }

        // إغلاق النافذة
        function closeModal() {
            document.getElementById('entry-modal').style.display = 'none';
        }

        // البحث في الجدول
        function searchTable() {
            updateTable();
        }

        // تصفية الجدول
        function filterTable() {
            updateTable();
        }

        // طباعة الجدول
        function printTable() {
            window.print();
        }

        // تصدير إلى PDF
        function exportToPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF('p', 'mm', 'a4');

            // إعداد الخط
            doc.setFont('Arial', 'normal');
            doc.setFontSize(16);

            // العنوان
            doc.text('الجمهورية الجزائرية الديمقراطية الشعبية', 105, 20, { align: 'center' });
            doc.text('مركز وقود المستقبل - عزيري عبد الله اسحاق', 105, 30, { align: 'center' });
            doc.setFontSize(12);
            doc.text('رقم: 463/2019', 105, 40, { align: 'center' });
            doc.text('إلى السيد: مدير الصناعة و المناجم لولاية المدية', 105, 50, { align: 'center' });
            doc.setFontSize(14);
            doc.text('جدول إرسال', 105, 65, { align: 'center' });

            // تاريخ الشهر
            const now = new Date();
            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            const currentMonthYear = `${monthNames[now.getMonth()]} ${now.getFullYear()}`;
            doc.setFontSize(10);
            doc.text(`تجدون طي هذه المراسلة الوثائق الخاصة بالسيارات المجهزة بغاز البترول المميع شهر ${currentMonthYear}.`, 105, 75, { align: 'center' });

            // إعداد الجدول
            const tableData = transmissionData.map((entry, index) => [
                entry.type,
                entry.tankNumber,
                entry.carType,
                entry.serialNumber,
                entry.registrationNumber,
                entry.ownerName,
                (index + 1).toString(),
                formatDate(entry.operationDate)
            ]);

            const headers = [
                'تركيب أو مراقبة',
                'رقم خزان الغاز',
                'الصنف',
                'الرقم التسلسلي',
                'رقم التسجيل',
                'الإسم و اللقب',
                'الرقم',
                'تاريخ العملية'
            ];

            // رسم الجدول
            doc.autoTable({
                head: [headers],
                body: tableData,
                startY: 85,
                styles: {
                    fontSize: 8,
                    cellPadding: 2,
                    halign: 'center'
                },
                headStyles: {
                    fillColor: [30, 58, 138],
                    textColor: 255,
                    fontStyle: 'bold'
                },
                alternateRowStyles: {
                    fillColor: [248, 250, 252]
                },
                margin: { top: 85, right: 10, bottom: 20, left: 10 }
            });

            // حفظ الملف
            const filename = `جدول_الإرسال_${currentMonthYear.replace(' ', '_')}.pdf`;
            doc.save(filename);

            showToast('تم تصدير جدول الإرسال إلى PDF بنجاح');
        }

        // مسح الجدول
        function clearTable() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                if (confirm('تأكيد أخير: سيتم حذف جميع العمليات نهائياً. هل تريد المتابعة؟')) {
                    transmissionData = [];
                    saveData();
                    updateTable();
                    showToast('تم مسح جدول الإرسال بنجاح');
                }
            }
        }

        // عرض رسالة
        function showToast(message, isSuccess = true) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${isSuccess ? '#10b981' : '#ef4444'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                z-index: 10000;
                font-weight: 500;
                max-width: 300px;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // معالج إرسال النموذج
        document.getElementById('entry-form').addEventListener('submit', function(e) {
            e.preventDefault();

            const entryData = {
                type: document.getElementById('entry-type').value,
                tankNumber: document.getElementById('entry-tank-number').value,
                carType: document.getElementById('entry-car-type').value,
                serialNumber: document.getElementById('entry-serial-number').value,
                registrationNumber: document.getElementById('entry-registration-number').value,
                ownerName: document.getElementById('entry-owner-name').value,
                operationDate: document.getElementById('entry-operation-date').value,
                createdAt: new Date().toISOString()
            };

            if (editingIndex >= 0) {
                // تعديل عملية موجودة
                transmissionData[editingIndex] = { ...transmissionData[editingIndex], ...entryData };
                showToast('تم تحديث العملية بنجاح');
            } else {
                // إضافة عملية جديدة
                transmissionData.push(entryData);
                showToast('تم إضافة العملية بنجاح');
            }

            saveData();
            updateTable();
            closeModal();
        });

        // البحث المباشر
        document.getElementById('search-input').addEventListener('input', function() {
            updateTable();
        });

        // إغلاق النافذة بالضغط على خارجها
        document.getElementById('entry-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // إغلاق النافذة بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });

        // العودة للنظام الرئيسي
        function goBackToMainSystem() {
            if (window.opener && !window.opener.closed) {
                // إذا كانت النافذة مفتوحة من النظام الرئيسي، أغلق هذه النافذة
                window.close();
            } else {
                // إذا لم تكن مفتوحة من النظام الرئيسي، انتقل إليه
                window.location.href = 'app/dashboard.html';
            }
        }

        // استقبال رسائل من النافذة الأصلية
        window.addEventListener('message', function(event) {
            if (event.data.type === 'configUpdate') {
                // تحديث الإعدادات
                console.log('تم تحديث الإعدادات من النظام الرئيسي');
            }
        });

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentMonthYear();
            updateTable();

            // تحميل الإعدادات
            const config = loadTransmissionConfig();
            if (config.companyName) {
                // تحديث اسم الشركة إذا كان متوفراً في الإعدادات
                const headerElements = document.querySelectorAll('h2');
                headerElements.forEach(el => {
                    if (el.textContent.includes('مركز وقود المستقبل')) {
                        el.textContent = config.companyName;
                    }
                });
            }
        });
    </script>
</body>
</html>
