<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فتح جدول الإرسال</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
            text-align: center;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #2196f3;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #1976d2;
        }
        .icon {
            font-size: 48px;
            color: #2196f3;
            margin-bottom: 20px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="icon">
            <i class="fas fa-table"></i>
        </div>
        <h1>جدول الإرسال</h1>
        <p>نظام إدارة عمليات التركيب والمراقبة</p>
        
        <div style="margin: 30px 0;">
            <button class="btn" onclick="openTransmissionTable()">
                <i class="fas fa-external-link-alt"></i> فتح جدول الإرسال
            </button>
            <br>
            <a href="dashboard.html" class="btn" style="background: #666;">
                <i class="fas fa-arrow-right"></i> العودة للنظام الرئيسي
            </a>
        </div>
        
        <div id="status" style="margin-top: 20px;"></div>
    </div>

    <script>
        function openTransmissionTable() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '<p style="color: #2196f3;">جاري فتح جدول الإرسال...</p>';
            
            try {
                // محاولة فتح من المسارات المختلفة
                const paths = [
                    '../transmission-table.html',
                    '../../transmission-table.html',
                    './transmission-table.html'
                ];
                
                let opened = false;
                
                // محاولة فتح من المسار الأول
                try {
                    window.open('../transmission-table.html', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
                    opened = true;
                    statusDiv.innerHTML = '<p style="color: green;">✅ تم فتح جدول الإرسال بنجاح</p>';
                } catch (e) {
                    console.log('فشل في فتح من المسار الأول');
                }
                
                // إذا لم ينجح، جرب المسارات الأخرى
                if (!opened) {
                    setTimeout(() => {
                        try {
                            window.open('../../transmission-table.html', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
                            statusDiv.innerHTML = '<p style="color: green;">✅ تم فتح جدول الإرسال من المسار البديل</p>';
                        } catch (e) {
                            statusDiv.innerHTML = `
                                <p style="color: red;">❌ تعذر فتح جدول الإرسال تلقائياً</p>
                                <p>يرجى فتح الملف يدوياً:</p>
                                <p><strong>transmission-table.html</strong></p>
                                <p>من مجلد التثبيت الرئيسي</p>
                            `;
                        }
                    }, 1000);
                }
                
            } catch (error) {
                statusDiv.innerHTML = `
                    <p style="color: red;">❌ خطأ: ${error.message}</p>
                    <p>يرجى فتح الملف transmission-table.html مباشرة</p>
                `;
            }
        }
        
        // محاولة فتح تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // انتظار ثانية واحدة ثم فتح جدول الإرسال تلقائياً
            setTimeout(() => {
                openTransmissionTable();
            }, 1000);
        });
    </script>
</body>
</html>
