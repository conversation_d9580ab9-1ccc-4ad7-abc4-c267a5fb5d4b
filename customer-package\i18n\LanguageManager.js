/**
 * نظام إدارة اللغات المزدوجة
 * Système de Gestion des Langues Bilingue
 * مؤسسة وقود المستقبل - Future Fuel Corporation
 */

class LanguageManager {
    constructor() {
        this.currentLanguage = 'ar'; // اللغة الافتراضية
        this.supportedLanguages = ['ar', 'fr'];
        this.translations = {};
        this.isRTL = true;
        
        this.loadTranslations();
        this.initializeLanguageSystem();
    }

    /**
     * تحميل الترجمات
     * Charger les traductions
     */
    loadTranslations() {
        this.translations = {
            // العناوين الرئيسية - Titres principaux
            'app_title': {
                'ar': 'مؤسسة وقود المستقبل',
                'fr': 'Future Fuel Corporation'
            },
            'dashboard': {
                'ar': 'لوحة التحكم',
                'fr': 'Tableau de Bord'
            },
            'transmission_table': {
                'ar': 'جدول الإرسال',
                'fr': 'Table de Transmission'
            },
            'customers': {
                'ar': 'العملاء',
                'fr': 'Clients'
            },
            'gas_cards': {
                'ar': 'بطاقات الغاز',
                'fr': 'Cartes de Gaz'
            },
            'employees': {
                'ar': 'الموظفين',
                'fr': 'Employés'
            },
            'products': {
                'ar': 'المنتجات',
                'fr': 'Produits'
            },
            'suppliers': {
                'ar': 'الموردين',
                'fr': 'Fournisseurs'
            },
            'sales': {
                'ar': 'المبيعات',
                'fr': 'Ventes'
            },
            'appointments': {
                'ar': 'المواعيد',
                'fr': 'Rendez-vous'
            },
            'reports': {
                'ar': 'التقارير',
                'fr': 'Rapports'
            },
            'settings': {
                'ar': 'الإعدادات',
                'fr': 'Paramètres'
            },

            // القوائم والتنقل - Menus et navigation
            'home': {
                'ar': 'الرئيسية',
                'fr': 'Accueil'
            },
            'back': {
                'ar': 'رجوع',
                'fr': 'Retour'
            },
            'next': {
                'ar': 'التالي',
                'fr': 'Suivant'
            },
            'previous': {
                'ar': 'السابق',
                'fr': 'Précédent'
            },
            'close': {
                'ar': 'إغلاق',
                'fr': 'Fermer'
            },
            'menu': {
                'ar': 'القائمة',
                'fr': 'Menu'
            },

            // العمليات الأساسية - Opérations de base
            'add': {
                'ar': 'إضافة',
                'fr': 'Ajouter'
            },
            'edit': {
                'ar': 'تعديل',
                'fr': 'Modifier'
            },
            'delete': {
                'ar': 'حذف',
                'fr': 'Supprimer'
            },
            'save': {
                'ar': 'حفظ',
                'fr': 'Enregistrer'
            },
            'cancel': {
                'ar': 'إلغاء',
                'fr': 'Annuler'
            },
            'search': {
                'ar': 'بحث',
                'fr': 'Rechercher'
            },
            'filter': {
                'ar': 'تصفية',
                'fr': 'Filtrer'
            },
            'export': {
                'ar': 'تصدير',
                'fr': 'Exporter'
            },
            'import': {
                'ar': 'استيراد',
                'fr': 'Importer'
            },
            'print': {
                'ar': 'طباعة',
                'fr': 'Imprimer'
            },

            // الحقول والبيانات - Champs et données
            'name': {
                'ar': 'الاسم',
                'fr': 'Nom'
            },
            'phone': {
                'ar': 'الهاتف',
                'fr': 'Téléphone'
            },
            'email': {
                'ar': 'البريد الإلكتروني',
                'fr': 'E-mail'
            },
            'address': {
                'ar': 'العنوان',
                'fr': 'Adresse'
            },
            'date': {
                'ar': 'التاريخ',
                'fr': 'Date'
            },
            'time': {
                'ar': 'الوقت',
                'fr': 'Heure'
            },
            'status': {
                'ar': 'الحالة',
                'fr': 'Statut'
            },
            'type': {
                'ar': 'النوع',
                'fr': 'Type'
            },
            'description': {
                'ar': 'الوصف',
                'fr': 'Description'
            },
            'notes': {
                'ar': 'ملاحظات',
                'fr': 'Notes'
            },

            // جدول الإرسال - Table de transmission
            'operation_type': {
                'ar': 'نوع العملية',
                'fr': 'Type d\'Opération'
            },
            'installation': {
                'ar': 'تركيب',
                'fr': 'Installation'
            },
            'monitoring': {
                'ar': 'مراقبة',
                'fr': 'Surveillance'
            },
            'tank_number': {
                'ar': 'رقم الخزان',
                'fr': 'Numéro de Réservoir'
            },
            'car_type': {
                'ar': 'نوع السيارة',
                'fr': 'Type de Véhicule'
            },
            'serial_number': {
                'ar': 'الرقم التسلسلي',
                'fr': 'Numéro de Série'
            },
            'registration_number': {
                'ar': 'رقم التسجيل',
                'fr': 'Numéro d\'Immatriculation'
            },
            'owner_name': {
                'ar': 'اسم المالك',
                'fr': 'Nom du Propriétaire'
            },
            'operation_date': {
                'ar': 'تاريخ العملية',
                'fr': 'Date d\'Opération'
            },
            'certificate_number': {
                'ar': 'رقم الشهادة',
                'fr': 'Numéro de Certificat'
            },

            // العملاء - Clients
            'customer_code': {
                'ar': 'رمز العميل',
                'fr': 'Code Client'
            },
            'customer_type': {
                'ar': 'نوع العميل',
                'fr': 'Type de Client'
            },
            'individual': {
                'ar': 'فرد',
                'fr': 'Particulier'
            },
            'company': {
                'ar': 'شركة',
                'fr': 'Entreprise'
            },
            'national_id': {
                'ar': 'رقم الهوية',
                'fr': 'Numéro d\'Identité'
            },
            'credit_limit': {
                'ar': 'الحد الائتماني',
                'fr': 'Limite de Crédit'
            },
            'current_balance': {
                'ar': 'الرصيد الحالي',
                'fr': 'Solde Actuel'
            },
            'registration_date': {
                'ar': 'تاريخ التسجيل',
                'fr': 'Date d\'Inscription'
            },

            // بطاقات الغاز - Cartes de gaz
            'card_number': {
                'ar': 'رقم البطاقة',
                'fr': 'Numéro de Carte'
            },
            'card_type': {
                'ar': 'نوع البطاقة',
                'fr': 'Type de Carte'
            },
            'regular': {
                'ar': 'عادية',
                'fr': 'Normale'
            },
            'gold': {
                'ar': 'ذهبية',
                'fr': 'Or'
            },
            'platinum': {
                'ar': 'بلاتينية',
                'fr': 'Platine'
            },
            'balance': {
                'ar': 'الرصيد',
                'fr': 'Solde'
            },
            'issue_date': {
                'ar': 'تاريخ الإصدار',
                'fr': 'Date d\'Émission'
            },
            'expiry_date': {
                'ar': 'تاريخ الانتهاء',
                'fr': 'Date d\'Expiration'
            },
            'daily_limit': {
                'ar': 'الحد اليومي',
                'fr': 'Limite Quotidienne'
            },
            'monthly_limit': {
                'ar': 'الحد الشهري',
                'fr': 'Limite Mensuelle'
            },

            // الحالات - États
            'active': {
                'ar': 'نشط',
                'fr': 'Actif'
            },
            'inactive': {
                'ar': 'غير نشط',
                'fr': 'Inactif'
            },
            'disabled': {
                'ar': 'معطل',
                'fr': 'Désactivé'
            },
            'blocked': {
                'ar': 'محظور',
                'fr': 'Bloqué'
            },
            'expired': {
                'ar': 'منتهي الصلاحية',
                'fr': 'Expiré'
            },
            'lost': {
                'ar': 'مفقود',
                'fr': 'Perdu'
            },

            // الأرقام والإحصائيات - Chiffres et statistiques
            'total': {
                'ar': 'الإجمالي',
                'fr': 'Total'
            },
            'count': {
                'ar': 'العدد',
                'fr': 'Nombre'
            },
            'today': {
                'ar': 'اليوم',
                'fr': 'Aujourd\'hui'
            },
            'this_month': {
                'ar': 'هذا الشهر',
                'fr': 'Ce Mois'
            },
            'this_year': {
                'ar': 'هذا العام',
                'fr': 'Cette Année'
            },
            'records': {
                'ar': 'سجلات',
                'fr': 'Enregistrements'
            },
            'operations': {
                'ar': 'عمليات',
                'fr': 'Opérations'
            },

            // الرسائل - Messages
            'success': {
                'ar': 'نجح',
                'fr': 'Succès'
            },
            'error': {
                'ar': 'خطأ',
                'fr': 'Erreur'
            },
            'warning': {
                'ar': 'تحذير',
                'fr': 'Avertissement'
            },
            'info': {
                'ar': 'معلومات',
                'fr': 'Information'
            },
            'confirm': {
                'ar': 'تأكيد',
                'fr': 'Confirmer'
            },
            'loading': {
                'ar': 'جاري التحميل...',
                'fr': 'Chargement...'
            },
            'no_data': {
                'ar': 'لا توجد بيانات',
                'fr': 'Aucune donnée'
            },
            'data_saved': {
                'ar': 'تم حفظ البيانات بنجاح',
                'fr': 'Données enregistrées avec succès'
            },
            'data_deleted': {
                'ar': 'تم حذف البيانات بنجاح',
                'fr': 'Données supprimées avec succès'
            },
            'confirm_delete': {
                'ar': 'هل أنت متأكد من الحذف؟',
                'fr': 'Êtes-vous sûr de vouloir supprimer?'
            },

            // قاعدة البيانات - Base de données
            'database': {
                'ar': 'قاعدة البيانات',
                'fr': 'Base de Données'
            },
            'backup': {
                'ar': 'نسخة احتياطية',
                'fr': 'Sauvegarde'
            },
            'restore': {
                'ar': 'استعادة',
                'fr': 'Restaurer'
            },
            'storage': {
                'ar': 'التخزين',
                'fr': 'Stockage'
            },
            'capacity': {
                'ar': 'السعة',
                'fr': 'Capacité'
            },
            'used_space': {
                'ar': 'المساحة المستخدمة',
                'fr': 'Espace Utilisé'
            },
            'available_space': {
                'ar': 'المساحة المتاحة',
                'fr': 'Espace Disponible'
            },

            // التقارير - Rapports
            'daily_report': {
                'ar': 'تقرير يومي',
                'fr': 'Rapport Quotidien'
            },
            'monthly_report': {
                'ar': 'تقرير شهري',
                'fr': 'Rapport Mensuel'
            },
            'yearly_report': {
                'ar': 'تقرير سنوي',
                'fr': 'Rapport Annuel'
            },
            'custom_report': {
                'ar': 'تقرير مخصص',
                'fr': 'Rapport Personnalisé'
            },
            'statistics': {
                'ar': 'الإحصائيات',
                'fr': 'Statistiques'
            },

            // الأزرار والإجراءات - Boutons et actions
            'new_record': {
                'ar': 'سجل جديد',
                'fr': 'Nouvel Enregistrement'
            },
            'view_details': {
                'ar': 'عرض التفاصيل',
                'fr': 'Voir les Détails'
            },
            'update': {
                'ar': 'تحديث',
                'fr': 'Mettre à Jour'
            },
            'refresh': {
                'ar': 'تحديث',
                'fr': 'Actualiser'
            },
            'clear': {
                'ar': 'مسح',
                'fr': 'Effacer'
            },
            'reset': {
                'ar': 'إعادة تعيين',
                'fr': 'Réinitialiser'
            },
            'submit': {
                'ar': 'إرسال',
                'fr': 'Soumettre'
            },
            'apply': {
                'ar': 'تطبيق',
                'fr': 'Appliquer'
            },

            // أيام الأسبوع - Jours de la semaine
            'sunday': {
                'ar': 'الأحد',
                'fr': 'Dimanche'
            },
            'monday': {
                'ar': 'الاثنين',
                'fr': 'Lundi'
            },
            'tuesday': {
                'ar': 'الثلاثاء',
                'fr': 'Mardi'
            },
            'wednesday': {
                'ar': 'الأربعاء',
                'fr': 'Mercredi'
            },
            'thursday': {
                'ar': 'الخميس',
                'fr': 'Jeudi'
            },
            'friday': {
                'ar': 'الجمعة',
                'fr': 'Vendredi'
            },
            'saturday': {
                'ar': 'السبت',
                'fr': 'Samedi'
            },

            // الشهور - Mois
            'january': {
                'ar': 'يناير',
                'fr': 'Janvier'
            },
            'february': {
                'ar': 'فبراير',
                'fr': 'Février'
            },
            'march': {
                'ar': 'مارس',
                'fr': 'Mars'
            },
            'april': {
                'ar': 'أبريل',
                'fr': 'Avril'
            },
            'may': {
                'ar': 'مايو',
                'fr': 'Mai'
            },
            'june': {
                'ar': 'يونيو',
                'fr': 'Juin'
            },
            'july': {
                'ar': 'يوليو',
                'fr': 'Juillet'
            },
            'august': {
                'ar': 'أغسطس',
                'fr': 'Août'
            },
            'september': {
                'ar': 'سبتمبر',
                'fr': 'Septembre'
            },
            'october': {
                'ar': 'أكتوبر',
                'fr': 'Octobre'
            },
            'november': {
                'ar': 'نوفمبر',
                'fr': 'Novembre'
            },
            'december': {
                'ar': 'ديسمبر',
                'fr': 'Décembre'
            },

            // النظام المزدوج اللغة - Système bilingue
            'bilingual_system': {
                'ar': 'النظام المزدوج اللغة',
                'fr': 'Système Bilingue'
            },
            'bilingual_subtitle': {
                'ar': 'عربي - فرنسي | نظام شامل متعدد اللغات',
                'fr': 'Arabe - Français | Système Complet Multilingue'
            },
            'supported_languages': {
                'ar': 'اللغات المدعومة',
                'fr': 'Langues Supportées'
            },
            'translated_terms': {
                'ar': 'مصطلح مترجم',
                'fr': 'Termes Traduits'
            },
            'interface_coverage': {
                'ar': 'تغطية الواجهة',
                'fr': 'Couverture Interface'
            },
            'instant_switching': {
                'ar': 'تبديل فوري',
                'fr': 'Commutation Instantanée'
            },
            'language_comparison': {
                'ar': 'مقارنة اللغات',
                'fr': 'Comparaison des Langues'
            },
            'main_features': {
                'ar': 'الميزات الرئيسية',
                'fr': 'Fonctionnalités Principales'
            },
            'instant_language_switch': {
                'ar': 'تبديل فوري للغة',
                'fr': 'Commutation Instantanée de Langue'
            },
            'complete_translation': {
                'ar': 'ترجمة شاملة للواجهة',
                'fr': 'Traduction Complète de l\'Interface'
            },
            'rtl_ltr_support': {
                'ar': 'دعم الكتابة من اليمين واليسار',
                'fr': 'Support RTL et LTR'
            },
            'date_number_format': {
                'ar': 'تنسيق التواريخ والأرقام',
                'fr': 'Format Dates et Nombres'
            },
            'dynamic_content': {
                'ar': 'محتوى ديناميكي',
                'fr': 'Contenu Dynamique'
            },
            'test_language': {
                'ar': 'اختبار اللغة',
                'fr': 'Tester la Langue'
            },
            'business_modules': {
                'ar': 'وحدات العمل',
                'fr': 'Modules Métier'
            },
            'customer_management': {
                'ar': 'إدارة العملاء',
                'fr': 'Gestion des Clients'
            },
            'open_dashboard': {
                'ar': 'فتح لوحة التحكم',
                'fr': 'Ouvrir le Tableau de Bord'
            },
            'database_system': {
                'ar': 'نظام قاعدة البيانات',
                'fr': 'Système de Base de Données'
            },
            'professional_database': {
                'ar': 'قاعدة بيانات احترافية',
                'fr': 'Base de Données Professionnelle'
            },
            'large_storage': {
                'ar': 'مساحة تخزين كبيرة',
                'fr': 'Grand Espace de Stockage'
            },
            'backup_restore': {
                'ar': 'نسخ احتياطي واستعادة',
                'fr': 'Sauvegarde et Restauration'
            },
            'data_export': {
                'ar': 'تصدير البيانات',
                'fr': 'Export de Données'
            },
            'advanced_search': {
                'ar': 'بحث متقدم',
                'fr': 'Recherche Avancée'
            },
            'database_admin': {
                'ar': 'إدارة قاعدة البيانات',
                'fr': 'Administration Base de Données'
            },
            'system_tools': {
                'ar': 'أدوات النظام',
                'fr': 'Outils Système'
            },
            'storage_monitor': {
                'ar': 'مراقب التخزين',
                'fr': 'Moniteur de Stockage'
            },
            'system_monitor': {
                'ar': 'مراقب النظام',
                'fr': 'Moniteur Système'
            },
            'capacity_calculator': {
                'ar': 'حاسبة السعة',
                'fr': 'Calculateur de Capacité'
            },
            'database_inspector': {
                'ar': 'فاحص قاعدة البيانات',
                'fr': 'Inspecteur Base de Données'
            },
            'integration_test': {
                'ar': 'اختبار التكامل',
                'fr': 'Test d\'Intégration'
            },
            'advanced_features': {
                'ar': 'الميزات المتقدمة',
                'fr': 'Fonctionnalités Avancées'
            },
            'smart_translation': {
                'ar': 'ترجمة ذكية',
                'fr': 'Traduction Intelligente'
            },
            'smart_translation_desc': {
                'ar': 'نظام ترجمة متقدم يدعم السياق والمصطلحات التقنية',
                'fr': 'Système de traduction avancé supportant le contexte et les termes techniques'
            },
            'cultural_adaptation': {
                'ar': 'تكيف ثقافي',
                'fr': 'Adaptation Culturelle'
            },
            'cultural_adaptation_desc': {
                'ar': 'تنسيق التواريخ والأرقام والعملات حسب الثقافة المحلية',
                'fr': 'Formatage des dates, nombres et devises selon la culture locale'
            },
            'responsive_design': {
                'ar': 'تصميم متجاوب',
                'fr': 'Design Responsive'
            },
            'responsive_design_desc': {
                'ar': 'واجهة تتكيف مع جميع أحجام الشاشات والأجهزة',
                'fr': 'Interface qui s\'adapte à toutes les tailles d\'écran et appareils'
            },
            'performance_optimized': {
                'ar': 'محسن للأداء',
                'fr': 'Optimisé pour la Performance'
            },
            'performance_optimized_desc': {
                'ar': 'تبديل سريع للغة بدون إعادة تحميل الصفحة',
                'fr': 'Commutation rapide de langue sans rechargement de page'
            },
            'start_system': {
                'ar': 'بدء استخدام النظام',
                'fr': 'Commencer à Utiliser le Système'
            },
            'storage_manager': {
                'ar': 'إدارة المساحة',
                'fr': 'Gestionnaire d\'Espace'
            }
        };
    }

    /**
     * تهيئة نظام اللغات
     * Initialiser le système de langues
     */
    initializeLanguageSystem() {
        // تحميل اللغة المحفوظة
        const savedLanguage = localStorage.getItem('selectedLanguage');
        if (savedLanguage && this.supportedLanguages.includes(savedLanguage)) {
            this.currentLanguage = savedLanguage;
        }

        // تحديث اتجاه النص
        this.updateTextDirection();

        console.log(`✅ تم تهيئة نظام اللغات - اللغة الحالية: ${this.currentLanguage}`);
        console.log(`✅ Système de langues initialisé - Langue actuelle: ${this.currentLanguage}`);
    }

    /**
     * تغيير اللغة
     * Changer la langue
     */
    setLanguage(language) {
        if (!this.supportedLanguages.includes(language)) {
            console.error(`اللغة ${language} غير مدعومة`);
            console.error(`Langue ${language} non supportée`);
            return false;
        }

        this.currentLanguage = language;
        this.updateTextDirection();
        
        // حفظ اللغة
        localStorage.setItem('selectedLanguage', language);
        
        // تحديث الواجهة
        this.updateInterface();
        
        console.log(`تم تغيير اللغة إلى: ${language}`);
        console.log(`Langue changée vers: ${language}`);
        
        return true;
    }

    /**
     * الحصول على الترجمة
     * Obtenir la traduction
     */
    t(key, params = {}) {
        const translation = this.translations[key];
        
        if (!translation) {
            console.warn(`مفتاح الترجمة غير موجود: ${key}`);
            console.warn(`Clé de traduction manquante: ${key}`);
            return key;
        }

        let text = translation[this.currentLanguage] || translation['ar'] || key;
        
        // استبدال المتغيرات
        Object.keys(params).forEach(param => {
            text = text.replace(`{${param}}`, params[param]);
        });
        
        return text;
    }

    /**
     * تحديث اتجاه النص
     * Mettre à jour la direction du texte
     */
    updateTextDirection() {
        this.isRTL = this.currentLanguage === 'ar';
        
        if (document.body) {
            document.body.dir = this.isRTL ? 'rtl' : 'ltr';
            document.body.style.fontFamily = this.isRTL ? 'Arial, sans-serif' : 'Arial, sans-serif';
        }

        if (document.documentElement) {
            document.documentElement.dir = this.isRTL ? 'rtl' : 'ltr';
        }
    }

    /**
     * تحديث الواجهة
     * Mettre à jour l'interface
     */
    updateInterface() {
        // تحديث جميع العناصر التي تحتوي على data-i18n
        const elements = document.querySelectorAll('[data-i18n]');
        
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);
            
            if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'search')) {
                element.placeholder = translation;
            } else {
                element.textContent = translation;
            }
        });

        // تحديث العناوين
        const titleElements = document.querySelectorAll('[data-i18n-title]');
        titleElements.forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });

        // إرسال حدث تغيير اللغة
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage, isRTL: this.isRTL }
        }));
    }

    /**
     * الحصول على اللغة الحالية
     * Obtenir la langue actuelle
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * التحقق من اتجاه النص
     * Vérifier la direction du texte
     */
    isRightToLeft() {
        return this.isRTL;
    }

    /**
     * تنسيق التاريخ حسب اللغة
     * Formater la date selon la langue
     */
    formatDate(date, options = {}) {
        const locale = this.currentLanguage === 'ar' ? 'ar-SA' : 'fr-FR';
        return new Date(date).toLocaleDateString(locale, options);
    }

    /**
     * تنسيق الرقم حسب اللغة
     * Formater le nombre selon la langue
     */
    formatNumber(number, options = {}) {
        const locale = this.currentLanguage === 'ar' ? 'ar-SA' : 'fr-FR';
        return new Number(number).toLocaleString(locale, options);
    }

    /**
     * تنسيق العملة حسب اللغة
     * Formater la devise selon la langue
     */
    formatCurrency(amount, currency = 'SAR') {
        const locale = this.currentLanguage === 'ar' ? 'ar-SA' : 'fr-FR';
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency
        }).format(amount);
    }
}

// إنشاء مثيل عام لإدارة اللغات
window.i18n = new LanguageManager();

// دالة مساعدة للترجمة
window.t = function(key, params = {}) {
    return window.i18n.t(key, params);
};

console.log('✅ تم تحميل نظام إدارة اللغات المزدوجة');
console.log('✅ Système de gestion des langues bilingue chargé');
