@echo off
title Future Fuel Corporation - Customer Package

echo.
echo ================================================================================
echo                    Future Fuel Corporation - Customer Package
echo                    مؤسسة وقود المستقبل - حزمة العميل
echo ================================================================================
echo.
echo Welcome to Future Fuel Corporation Management System!
echo مرحباً بك في نظام إدارة مؤسسة وقود المستقبل!
echo.
echo This package contains everything you need to install and run the system.
echo تحتوي هذه الحزمة على كل ما تحتاجه لتثبيت وتشغيل النظام.
echo.
echo ================================================================================
echo                              Installation Options
echo                              خيارات التثبيت
echo ================================================================================
echo.
echo 1. Install System (تثبيت النظام) - install.bat
echo 2. Quick Start (البدء السريع) - QuickStart.bat
echo 3. Demo Trial (تجربة مجانية) - Demo-Login.bat
echo 4. Transmission Table (جدول الإرسال) - TransmissionTable-Launcher.bat
echo 5. Read Documentation (قراءة التوثيق) - README.txt
echo 6. View License (عرض الترخيص) - license.txt
echo 7. Login Guide (دليل تسجيل الدخول) - LOGIN_GUIDE.txt
echo 8. Exit (خروج)
echo.

set /p "choice=Choose an option (اختر خياراً) [1-8]: "

if "%choice%"=="1" (
    echo Starting installation...
    call install.bat
) else if "%choice%"=="2" (
    echo Starting Quick Start...
    call QuickStart.bat
) else if "%choice%"=="3" (
    echo Starting Demo Trial...
    call Demo-Login.bat
) else if "%choice%"=="4" (
    echo Starting Transmission Table...
    call TransmissionTable-Launcher.bat
) else if "%choice%"=="5" (
    echo Opening documentation...
    start notepad README.txt
) else if "%choice%"=="6" (
    echo Opening license...
    start notepad license.txt
) else if "%choice%"=="7" (
    echo Opening login guide...
    start notepad LOGIN_GUIDE.txt
) else if "%choice%"=="8" (
    echo Goodbye!
    exit
) else (
    echo Invalid choice. Please try again.
    pause
    goto start
)

pause
