@echo off
chcp 65001 >nul
title اختبار سريع - جدول الإرسال
color 0A

echo.
echo ================================================================================
echo                    اختبار سريع - جدول الإرسال
echo                    Quick Test - Transmission Table
echo ================================================================================
echo.

set "INSTALL_DIR=C:\Users\<USER>\Future Fuel Management"

echo 🔍 التحقق من ملفات جدول الإرسال...
echo.

if exist "%INSTALL_DIR%\transmission-table.html" (
    echo ✅ transmission-table.html موجود
) else (
    echo ❌ transmission-table.html غير موجود
)

if exist "%INSTALL_DIR%\app\dashboard.html" (
    echo ✅ dashboard.html موجود
) else (
    echo ❌ dashboard.html غير موجود
)

if exist "%INSTALL_DIR%\app\open-transmission-table.html" (
    echo ✅ open-transmission-table.html موجود
) else (
    echo ❌ open-transmission-table.html غير موجود
)

echo.
echo 🚀 خيارات التشغيل:
echo.
echo 1. فتح النظام الرئيسي (dashboard.html)
echo 2. فتح جدول الإرسال مباشرة (transmission-table.html)
echo 3. فتح صفحة اختبار جدول الإرسال
echo 4. فتح مجلد التثبيت
echo 5. خروج
echo.

set /p "choice=اختر رقماً [1-5]: "

if "%choice%"=="1" (
    echo.
    echo 🚀 فتح النظام الرئيسي...
    start "" "%INSTALL_DIR%\app\dashboard.html"
) else if "%choice%"=="2" (
    echo.
    echo 📋 فتح جدول الإرسال مباشرة...
    start "" "%INSTALL_DIR%\transmission-table.html"
) else if "%choice%"=="3" (
    echo.
    echo 🧪 فتح صفحة اختبار جدول الإرسال...
    start "" "%INSTALL_DIR%\test-transmission-integration.html"
) else if "%choice%"=="4" (
    echo.
    echo 📁 فتح مجلد التثبيت...
    start "" "%INSTALL_DIR%"
) else if "%choice%"=="5" (
    echo.
    echo 👋 وداعاً!
    exit
) else (
    echo.
    echo ❌ خيار غير صحيح
    pause
    goto start
)

echo.
echo ================================================================================
echo                              تعليمات مهمة
echo ================================================================================
echo.
echo 📋 للوصول لجدول الإرسال من النظام الرئيسي:
echo.
echo 1️⃣ من القائمة الجانبية:
echo    انقر على "جدول الإرسال" في القائمة اليسرى
echo.
echo 2️⃣ من لوحة التحكم:
echo    انقر على بطاقة "جدول الإرسال" في الصفحة الرئيسية
echo.
echo 3️⃣ تشغيل مباشر:
echo    انقر نقراً مزدوجاً على "transmission-table.html"
echo.
echo 🔧 إذا لم يعمل جدول الإرسال:
echo    1. تأكد من وجود ملف transmission-table.html
echo    2. جرب فتحه مباشرة من مجلد التثبيت
echo    3. تأكد من تفعيل JavaScript في المتصفح
echo.

pause
