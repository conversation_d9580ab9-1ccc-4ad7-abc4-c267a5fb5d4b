<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="app_title">النظام المزدوج اللغة - مؤسسة وقود المستقبل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            position: relative;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: bold;
        }
        .header p {
            margin: 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .demo-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        .demo-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .demo-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }
        .demo-card h3 {
            margin-top: 0;
            color: #667eea;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .demo-card .icon {
            font-size: 1.8rem;
            color: #764ba2;
        }
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .feature-list li:before {
            content: "✅";
            font-size: 1.2rem;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            margin: 5px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.info {
            background: #17a2b8;
        }
        .language-demo {
            background: white;
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
        }
        .language-demo h3 {
            text-align: center;
            color: #667eea;
            margin-bottom: 25px;
        }
        .demo-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }
        .demo-column {
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .demo-column.arabic {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            direction: rtl;
            text-align: right;
        }
        .demo-column.french {
            background: linear-gradient(135deg, #e8f0ff 0%, #f0f6ff 100%);
            direction: ltr;
            text-align: left;
        }
        .demo-column h4 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 14px;
        }
        .demo-table th, .demo-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .demo-table th {
            background: #667eea;
            color: white;
        }
        .demo-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .features-showcase {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .feature-card h5 {
            margin-top: 0;
            color: #667eea;
            font-size: 1.1rem;
        }
        .feature-card p {
            margin-bottom: 0;
            color: #666;
            line-height: 1.5;
        }
        @media (max-width: 768px) {
            .demo-content {
                grid-template-columns: 1fr;
            }
            .header h1 {
                font-size: 2rem;
            }
            .demo-sections {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 data-i18n="bilingual_system">🌐 النظام المزدوج اللغة</h1>
            <p data-i18n="bilingual_subtitle">عربي - فرنسي | Arabe - Français</p>
        </div>

        <!-- إحصائيات النظام -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">2</div>
                <div class="stat-label" data-i18n="supported_languages">اللغات المدعومة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">500+</div>
                <div class="stat-label" data-i18n="translated_terms">مصطلح مترجم</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label" data-i18n="interface_coverage">تغطية الواجهة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">⚡</div>
                <div class="stat-label" data-i18n="instant_switching">تبديل فوري</div>
            </div>
        </div>

        <!-- عرض المقارنة -->
        <div class="language-demo">
            <h3 data-i18n="language_comparison">مقارنة اللغات - Comparaison des Langues</h3>
            <div class="demo-content">
                <div class="demo-column arabic">
                    <h4>🇸🇦 العربية</h4>
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>العملاء</th>
                                <th>بطاقات الغاز</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>أحمد محمد</td>
                                <td>GC001234</td>
                                <td>تركيب</td>
                            </tr>
                            <tr>
                                <td>فاطمة علي</td>
                                <td>GC001235</td>
                                <td>مراقبة</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <ul class="feature-list">
                        <li>إدارة العملاء</li>
                        <li>بطاقات الغاز</li>
                        <li>جدول الإرسال</li>
                        <li>التقارير</li>
                        <li>الإعدادات</li>
                    </ul>
                </div>
                
                <div class="demo-column french">
                    <h4>🇫🇷 Français</h4>
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>Clients</th>
                                <th>Cartes de Gaz</th>
                                <th>Opérations</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Ahmed Mohammed</td>
                                <td>GC001234</td>
                                <td>Installation</td>
                            </tr>
                            <tr>
                                <td>Fatima Ali</td>
                                <td>GC001235</td>
                                <td>Surveillance</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <ul class="feature-list">
                        <li>Gestion des Clients</li>
                        <li>Cartes de Gaz</li>
                        <li>Table de Transmission</li>
                        <li>Rapports</li>
                        <li>Paramètres</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- أقسام العرض -->
        <div class="demo-sections">
            <div class="demo-card">
                <h3><span class="icon">🎯</span> <span data-i18n="main_features">الميزات الرئيسية</span></h3>
                <ul class="feature-list">
                    <li data-i18n="instant_language_switch">تبديل فوري للغة</li>
                    <li data-i18n="complete_translation">ترجمة شاملة للواجهة</li>
                    <li data-i18n="rtl_ltr_support">دعم الكتابة من اليمين واليسار</li>
                    <li data-i18n="date_number_format">تنسيق التواريخ والأرقام</li>
                    <li data-i18n="dynamic_content">محتوى ديناميكي</li>
                </ul>
                <button class="btn" onclick="testLanguageSwitch()">
                    <i class="fas fa-language"></i> <span data-i18n="test_language">اختبار اللغة</span>
                </button>
            </div>

            <div class="demo-card">
                <h3><span class="icon">🏢</span> <span data-i18n="business_modules">وحدات العمل</span></h3>
                <ul class="feature-list">
                    <li data-i18n="customer_management">إدارة العملاء</li>
                    <li data-i18n="gas_cards">بطاقات الغاز</li>
                    <li data-i18n="transmission_table">جدول الإرسال</li>
                    <li data-i18n="appointments">المواعيد</li>
                    <li data-i18n="reports">التقارير</li>
                </ul>
                <a href="app/dashboard.html" class="btn">
                    <i class="fas fa-tachometer-alt"></i> <span data-i18n="open_dashboard">فتح لوحة التحكم</span>
                </a>
            </div>

            <div class="demo-card">
                <h3><span class="icon">📊</span> <span data-i18n="database_system">نظام قاعدة البيانات</span></h3>
                <ul class="feature-list">
                    <li data-i18n="professional_database">قاعدة بيانات احترافية</li>
                    <li data-i18n="large_storage">مساحة تخزين كبيرة</li>
                    <li data-i18n="backup_restore">نسخ احتياطي واستعادة</li>
                    <li data-i18n="data_export">تصدير البيانات</li>
                    <li data-i18n="advanced_search">بحث متقدم</li>
                </ul>
                <a href="database/DatabaseAdmin.html" class="btn info">
                    <i class="fas fa-database"></i> <span data-i18n="database_admin">إدارة قاعدة البيانات</span>
                </a>
            </div>

            <div class="demo-card">
                <h3><span class="icon">🔧</span> <span data-i18n="system_tools">أدوات النظام</span></h3>
                <ul class="feature-list">
                    <li data-i18n="storage_monitor">مراقب التخزين</li>
                    <li data-i18n="system_monitor">مراقب النظام</li>
                    <li data-i18n="capacity_calculator">حاسبة السعة</li>
                    <li data-i18n="database_inspector">فاحص قاعدة البيانات</li>
                    <li data-i18n="integration_test">اختبار التكامل</li>
                </ul>
                <button class="btn secondary" onclick="openSystemTools()">
                    <i class="fas fa-tools"></i> <span data-i18n="system_tools">أدوات النظام</span>
                </button>
            </div>
        </div>

        <!-- عرض الميزات -->
        <div class="features-showcase">
            <h3 data-i18n="advanced_features">الميزات المتقدمة - Fonctionnalités Avancées</h3>
            <div class="features-grid">
                <div class="feature-card">
                    <h5 data-i18n="smart_translation">ترجمة ذكية</h5>
                    <p data-i18n="smart_translation_desc">نظام ترجمة متقدم يدعم السياق والمصطلحات التقنية</p>
                </div>
                <div class="feature-card">
                    <h5 data-i18n="cultural_adaptation">تكيف ثقافي</h5>
                    <p data-i18n="cultural_adaptation_desc">تنسيق التواريخ والأرقام والعملات حسب الثقافة المحلية</p>
                </div>
                <div class="feature-card">
                    <h5 data-i18n="responsive_design">تصميم متجاوب</h5>
                    <p data-i18n="responsive_design_desc">واجهة تتكيف مع جميع أحجام الشاشات والأجهزة</p>
                </div>
                <div class="feature-card">
                    <h5 data-i18n="performance_optimized">محسن للأداء</h5>
                    <p data-i18n="performance_optimized_desc">تبديل سريع للغة بدون إعادة تحميل الصفحة</p>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div style="text-align: center; margin: 40px 0;">
            <a href="app/dashboard.html" class="btn success">
                <i class="fas fa-rocket"></i> <span data-i18n="start_system">بدء استخدام النظام</span>
            </a>
            <a href="transmission-table.html" class="btn">
                <i class="fas fa-table"></i> <span data-i18n="transmission_table">جدول الإرسال</span>
            </a>
            <a href="database/LargeStorageManager.html" class="btn info">
                <i class="fas fa-expand-arrows-alt"></i> <span data-i18n="storage_manager">إدارة المساحة</span>
            </a>
        </div>
    </div>

    <!-- تحميل نظام اللغات -->
    <script src="i18n/LanguageManager.js"></script>
    <script src="i18n/LanguageSwitcher.js"></script>
    
    <script>
        // تهيئة النظام المزدوج اللغة
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث النصوص عند تغيير اللغة
            window.addEventListener('languageChanged', function(e) {
                updateDemoLanguage(e.detail.language);
            });
            
            // تحديث اللغة الأولية
            setTimeout(() => {
                updateDemoLanguage(window.i18n.getCurrentLanguage());
            }, 500);
        });
        
        function updateDemoLanguage(language) {
            console.log(`تحديث لغة العرض إلى: ${language}`);
            
            // تحديث اتجاه النص للأعمدة
            const arabicColumn = document.querySelector('.demo-column.arabic');
            const frenchColumn = document.querySelector('.demo-column.french');
            
            if (language === 'fr') {
                // إبراز العمود الفرنسي
                if (frenchColumn) {
                    frenchColumn.style.border = '3px solid #667eea';
                    frenchColumn.style.background = 'linear-gradient(135deg, #e8f0ff 0%, #f0f6ff 100%)';
                }
                if (arabicColumn) {
                    arabicColumn.style.border = '1px solid #e9ecef';
                    arabicColumn.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #f8f9fa 100%)';
                }
            } else {
                // إبراز العمود العربي
                if (arabicColumn) {
                    arabicColumn.style.border = '3px solid #667eea';
                    arabicColumn.style.background = 'linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%)';
                }
                if (frenchColumn) {
                    frenchColumn.style.border = '1px solid #e9ecef';
                    frenchColumn.style.background = 'linear-gradient(135deg, #f8f9fa 0%, #f8f9fa 100%)';
                }
            }
        }
        
        function testLanguageSwitch() {
            const currentLang = window.i18n.getCurrentLanguage();
            const newLang = currentLang === 'ar' ? 'fr' : 'ar';
            window.i18n.setLanguage(newLang);
            
            // إظهار رسالة تأكيد
            const message = newLang === 'ar' ? 
                'تم التبديل إلى العربية!' : 
                'Basculé vers le français!';
            
            showNotification(message);
        }
        
        function openSystemTools() {
            const tools = [
                'Storage-Monitor.html',
                'System-Monitor.html', 
                'Capacity-Calculator.html',
                'Database-Inspector.html',
                'Check-Integration.html'
            ];
            
            tools.forEach((tool, index) => {
                setTimeout(() => {
                    window.open(tool, '_blank');
                }, index * 500);
            });
        }
        
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
                z-index: 10000;
                font-weight: 500;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
        
        console.log('✅ تم تحميل عرض النظام المزدوج اللغة');
        console.log('✅ Démonstration du système bilingue chargée');
    </script>
</body>
</html>
