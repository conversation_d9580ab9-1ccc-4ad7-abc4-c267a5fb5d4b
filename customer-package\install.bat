@echo off
chcp 65001 >nul
title مؤسسة وقود المستقبل - معالج التثبيت
color 0A

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ تم تشغيل المعالج بصلاحيات المدير
) else (
    echo ⚠️ يتطلب تشغيل هذا المعالج بصلاحيات المدير
    echo يرجى النقر بالزر الأيمن واختيار "تشغيل كمسؤول"
    pause
    exit /b 1
)

echo.
echo ================================================================================
echo                    مؤسسة وقود المستقبل - معالج التثبيت
echo                    Future Fuel Corporation - Installation Wizard
echo ================================================================================
echo.
echo مرحباً بك في معالج تثبيت نظام إدارة مؤسسة وقود المستقبل
echo.
echo هذا المعالج سيقوم بتثبيت النظام على جهازك مع جميع المكونات المطلوبة
echo.
echo ================================================================================
echo.

:: التحقق من متطلبات النظام
echo 🔍 فحص متطلبات النظام...
echo.

:: فحص نظام التشغيل
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo ✓ نظام التشغيل: Windows %VERSION%

:: فحص المساحة المتاحة
for /f "tokens=3" %%a in ('dir /-c %SystemDrive%\ ^| find "bytes free"') do set FREESPACE=%%a
if %FREESPACE% LSS 1073741824 (
    echo ❌ مساحة القرص غير كافية. يتطلب 1 GB على الأقل
    pause
    exit /b 1
) else (
    echo ✓ مساحة القرص: كافية
)

:: فحص الذاكرة
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do (
    set MEMORY=%%p
    goto :memory_done
)
:memory_done
if %MEMORY% LSS 4294967296 (
    echo ⚠️ الذاكرة أقل من المستحسن (4 GB)
) else (
    echo ✓ الذاكرة: كافية
)

echo.
echo ================================================================================
echo.

:: اختيار مجلد التثبيت
set "INSTALL_DIR=C:\FutureFuel"
echo 📁 مجلد التثبيت الافتراضي: %INSTALL_DIR%
echo.
set /p "CUSTOM_DIR=أدخل مجلد مخصص أو اضغط Enter للافتراضي: "
if not "%CUSTOM_DIR%"=="" set "INSTALL_DIR=%CUSTOM_DIR%"

echo.
echo سيتم التثبيت في: %INSTALL_DIR%
echo.
set /p "CONFIRM=هل تريد المتابعة؟ (Y/N): "
if /i not "%CONFIRM%"=="Y" if /i not "%CONFIRM%"=="نعم" (
    echo تم إلغاء التثبيت
    pause
    exit /b 0
)

echo.
echo ================================================================================
echo                              بدء التثبيت
echo ================================================================================
echo.

:: إنشاء مجلد التثبيت
echo 📁 إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%INSTALL_DIR%\app" mkdir "%INSTALL_DIR%\app"
if not exist "%INSTALL_DIR%\assets" mkdir "%INSTALL_DIR%\assets"
if not exist "%INSTALL_DIR%\data" mkdir "%INSTALL_DIR%\data"
if not exist "%INSTALL_DIR%\backup" mkdir "%INSTALL_DIR%\backup"
if not exist "%INSTALL_DIR%\logs" mkdir "%INSTALL_DIR%\logs"
if not exist "%INSTALL_DIR%\updates" mkdir "%INSTALL_DIR%\updates"
echo ✓ تم إنشاء المجلدات

:: نسخ الملفات
echo.
echo 📋 نسخ ملفات التطبيق...
xcopy /E /I /Y "app\*" "%INSTALL_DIR%\app\" >nul
xcopy /E /I /Y "assets\*" "%INSTALL_DIR%\assets\" >nul
copy /Y "config.json" "%INSTALL_DIR%\" >nul
copy /Y "license.txt" "%INSTALL_DIR%\" >nul
copy /Y "README.txt" "%INSTALL_DIR%\" >nul
copy /Y "uninstall.bat" "%INSTALL_DIR%\" >nul
echo ✓ تم نسخ الملفات الأساسية

:: إنشاء ملف التطبيق الرئيسي
echo.
echo 🔧 إنشاء التطبيق الرئيسي...
(
echo @echo off
echo chcp 65001 ^>nul
echo title مؤسسة وقود المستقبل
echo cd /d "%INSTALL_DIR%"
echo start "" "app\index.html"
) > "%INSTALL_DIR%\FutureFuel.bat"
echo ✓ تم إنشاء ملف التطبيق

:: إنشاء اختصار على سطح المكتب
echo.
echo 🖥️ إنشاء اختصار سطح المكتب...
set "DESKTOP=%USERPROFILE%\Desktop"
(
echo [InternetShortcut]
echo URL=file:///%INSTALL_DIR:\=/%/app/index.html
echo IconFile=%INSTALL_DIR%\assets\icon.ico
echo IconIndex=0
) > "%DESKTOP%\مؤسسة وقود المستقبل.url"
echo ✓ تم إنشاء الاختصار

:: إنشاء اختصار في قائمة ابدأ
echo.
echo 📋 إنشاء اختصار قائمة ابدأ...
set "STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
if not exist "%STARTMENU%\مؤسسة وقود المستقبل" mkdir "%STARTMENU%\مؤسسة وقود المستقبل"
(
echo [InternetShortcut]
echo URL=file:///%INSTALL_DIR:\=/%/app/index.html
echo IconFile=%INSTALL_DIR%\assets\icon.ico
echo IconIndex=0
) > "%STARTMENU%\مؤسسة وقود المستقبل\مؤسسة وقود المستقبل.url"
echo ✓ تم إنشاء اختصار قائمة ابدأ

:: تسجيل البرنامج في النظام
echo.
echo 📝 تسجيل البرنامج في النظام...
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayName" /t REG_SZ /d "مؤسسة وقود المستقبل" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "DisplayVersion" /t REG_SZ /d "2.2.0" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "Publisher" /t REG_SZ /d "Future Fuel Corporation" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\FutureFuel" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\uninstall.bat" /f >nul
echo ✓ تم تسجيل البرنامج

:: إنشاء ملف معلومات التثبيت
echo.
echo 📄 إنشاء ملف معلومات التثبيت...
(
echo {
echo   "installDate": "%DATE% %TIME%",
echo   "installPath": "%INSTALL_DIR%",
echo   "version": "2.2.0",
echo   "computerName": "%COMPUTERNAME%",
echo   "userName": "%USERNAME%",
echo   "osVersion": "%VERSION%",
echo   "installId": "%RANDOM%%RANDOM%"
echo }
) > "%INSTALL_DIR%\install-info.json"
echo ✓ تم إنشاء ملف المعلومات

echo.
echo ================================================================================
echo                              اكتمل التثبيت
echo ================================================================================
echo.
echo 🎉 تم تثبيت نظام مؤسسة وقود المستقبل بنجاح!
echo.
echo 📍 مجلد التثبيت: %INSTALL_DIR%
echo 🖥️ اختصار سطح المكتب: تم إنشاؤه
echo 📋 قائمة ابدأ: تم إضافته
echo.
echo ================================================================================
echo                              الخطوات التالية
echo ================================================================================
echo.
echo 1. انقر على اختصار "مؤسسة وقود المستقبل" على سطح المكتب
echo 2. أدخل كود الترخيص المرسل إليك
echo 3. ابدأ في استخدام النظام
echo.
echo للمساعدة: <EMAIL>
echo الهاتف: +966-11-123-4567
echo.
echo ================================================================================
echo.

set /p "LAUNCH=هل تريد تشغيل التطبيق الآن؟ (Y/N): "
if /i "%LAUNCH%"=="Y" if /i "%LAUNCH%"=="نعم" (
    echo.
    echo 🚀 تشغيل التطبيق...
    start "" "%INSTALL_DIR%\app\index.html"
)

echo.
echo شكراً لاختيارك مؤسسة وقود المستقبل!
echo.
pause
