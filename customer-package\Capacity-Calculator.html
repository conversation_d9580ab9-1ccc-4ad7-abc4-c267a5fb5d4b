<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة سعة قاعدة البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #e91e63, #9c27b0);
            color: white;
            border-radius: 10px;
        }
        .calculator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .calc-card {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #e91e63;
        }
        .calc-card h3 {
            margin-top: 0;
            color: #e91e63;
        }
        .input-group {
            margin: 15px 0;
        }
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .result-display {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 2px solid #e91e63;
            margin: 15px 0;
        }
        .result-number {
            font-size: 24px;
            font-weight: bold;
            color: #e91e63;
        }
        .result-label {
            color: #666;
            margin-top: 5px;
        }
        .btn {
            background: #e91e63;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover {
            background: #c2185b;
        }
        .progress-bar {
            width: 100%;
            height: 30px;
            background: #e0e0e0;
            border-radius: 15px;
            overflow: hidden;
            margin: 15px 0;
            position: relative;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #ff9800, #f44336);
            transition: width 0.3s ease;
            border-radius: 15px;
        }
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            color: #333;
        }
        .warning {
            background: #fff3e0;
            color: #f57c00;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ff9800;
            margin: 15px 0;
        }
        .danger {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #f44336;
            margin: 15px 0;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #4caf50;
            margin: 15px 0;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        .table th {
            background: #e91e63;
            color: white;
        }
        .table tr:nth-child(even) {
            background: #f9f9f9;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-calculator"></i> حاسبة سعة قاعدة البيانات</h1>
            <p>احسب كم زبون وبطاقة يمكن حفظها في قاعدة البيانات</p>
        </div>

        <!-- الحاسبة التفاعلية -->
        <div class="calculator-grid">
            <div class="calc-card">
                <h3>📊 حاسبة السعة</h3>
                
                <div class="input-group">
                    <label>عدد الزبائن:</label>
                    <input type="number" id="customers-count" value="1000" oninput="calculateCapacity()">
                </div>
                
                <div class="input-group">
                    <label>عدد بطاقات الغاز:</label>
                    <input type="number" id="cards-count" value="1500" oninput="calculateCapacity()">
                </div>
                
                <div class="input-group">
                    <label>عدد عمليات الإرسال:</label>
                    <input type="number" id="transmission-count" value="2000" oninput="calculateCapacity()">
                </div>
                
                <button class="btn" onclick="calculateCapacity()">
                    <i class="fas fa-calculator"></i> احسب السعة
                </button>
            </div>

            <div class="calc-card">
                <h3>📈 النتائج</h3>
                
                <div class="result-display">
                    <div class="result-number" id="total-size">0 MB</div>
                    <div class="result-label">الحجم الإجمالي</div>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="capacity-progress"></div>
                    <div class="progress-text" id="capacity-percentage">0%</div>
                </div>
                
                <div id="capacity-status"></div>
            </div>
        </div>

        <!-- جدول السعات -->
        <div style="margin: 30px 0;">
            <h3>📋 جدول السعات المرجعية</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>نوع السجل</th>
                        <th>حجم السجل الواحد</th>
                        <th>1000 سجل</th>
                        <th>5000 سجل</th>
                        <th>10000 سجل</th>
                        <th>الحد الأقصى الآمن</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><i class="fas fa-user"></i> زبون</td>
                        <td>450 بايت</td>
                        <td>450 KB</td>
                        <td>2.25 MB</td>
                        <td>4.5 MB</td>
                        <td>~11,000 زبون</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-credit-card"></i> بطاقة غاز</td>
                        <td>300 بايت</td>
                        <td>300 KB</td>
                        <td>1.5 MB</td>
                        <td>3 MB</td>
                        <td>~16,500 بطاقة</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-table"></i> عملية إرسال</td>
                        <td>400 بايت</td>
                        <td>400 KB</td>
                        <td>2 MB</td>
                        <td>4 MB</td>
                        <td>~12,500 عملية</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- سيناريوهات جاهزة -->
        <div style="margin: 30px 0;">
            <h3>🎯 سيناريوهات جاهزة</h3>
            <div class="calculator-grid">
                <div class="calc-card">
                    <h4>محطة صغيرة</h4>
                    <p>500 زبون + 800 بطاقة + 1000 عملية</p>
                    <button class="btn" onclick="loadScenario(500, 800, 1000)">تطبيق</button>
                    <div style="margin-top: 10px; color: #4caf50; font-weight: bold;">
                        الحجم المتوقع: ~865 KB
                    </div>
                </div>
                
                <div class="calc-card">
                    <h4>محطة متوسطة</h4>
                    <p>2000 زبون + 3500 بطاقة + 5000 عملية</p>
                    <button class="btn" onclick="loadScenario(2000, 3500, 5000)">تطبيق</button>
                    <div style="margin-top: 10px; color: #ff9800; font-weight: bold;">
                        الحجم المتوقع: ~3.95 MB
                    </div>
                </div>
                
                <div class="calc-card">
                    <h4>محطة كبيرة</h4>
                    <p>5000 زبون + 8000 بطاقة + 10000 عملية</p>
                    <button class="btn" onclick="loadScenario(5000, 8000, 10000)">تطبيق</button>
                    <div style="margin-top: 10px; color: #f44336; font-weight: bold;">
                        الحجم المتوقع: ~8.65 MB ⚠️
                    </div>
                </div>
            </div>
        </div>

        <!-- نصائح التحسين -->
        <div style="margin: 30px 0;">
            <h3>💡 نصائح لتحسين السعة</h3>
            <div class="success">
                <h4>✅ استراتيجيات التحسين:</h4>
                <ul>
                    <li><strong>الأرشفة الدورية:</strong> انقل العمليات الأقدم من سنة إلى أرشيف خارجي</li>
                    <li><strong>ضغط البيانات:</strong> استخدم ضغط JSON لتوفير 20-30% من المساحة</li>
                    <li><strong>تنظيف البيانات:</strong> احذف السجلات المحذوفة والمكررة</li>
                    <li><strong>تحسين الحقول:</strong> استخدم اختصارات للحقول الطويلة</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // أحجام السجلات بالبايت
        const RECORD_SIZES = {
            customer: 450,
            gasCard: 300,
            transmission: 400
        };

        // الحد الأقصى الآمن (5 MB)
        const MAX_SAFE_SIZE = 5 * 1024 * 1024; // 5 MB بالبايت

        function calculateCapacity() {
            const customersCount = parseInt(document.getElementById('customers-count').value) || 0;
            const cardsCount = parseInt(document.getElementById('cards-count').value) || 0;
            const transmissionCount = parseInt(document.getElementById('transmission-count').value) || 0;

            // حساب الأحجام
            const customersSize = customersCount * RECORD_SIZES.customer;
            const cardsSize = cardsCount * RECORD_SIZES.gasCard;
            const transmissionSize = transmissionCount * RECORD_SIZES.transmission;
            
            const totalSize = customersSize + cardsSize + transmissionSize;
            const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
            const percentage = ((totalSize / MAX_SAFE_SIZE) * 100).toFixed(1);

            // تحديث النتائج
            document.getElementById('total-size').textContent = totalSizeMB + ' MB';
            
            const progressFill = document.getElementById('capacity-progress');
            const progressText = document.getElementById('capacity-percentage');
            
            progressFill.style.width = Math.min(percentage, 100) + '%';
            progressText.textContent = percentage + '%';

            // تحديث حالة السعة
            updateCapacityStatus(percentage, totalSizeMB);
        }

        function updateCapacityStatus(percentage, sizeMB) {
            const statusDiv = document.getElementById('capacity-status');
            
            if (percentage < 50) {
                statusDiv.innerHTML = `
                    <div class="success">
                        <i class="fas fa-check-circle"></i>
                        <strong>ممتاز!</strong> السعة في حالة جيدة (${percentage}% مستخدمة)
                    </div>
                `;
            } else if (percentage < 75) {
                statusDiv.innerHTML = `
                    <div class="warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> تم استخدام أكثر من نصف السعة (${percentage}%)
                        <br>يُنصح بمراقبة الاستهلاك والتخطيط للأرشفة
                    </div>
                `;
            } else if (percentage < 90) {
                statusDiv.innerHTML = `
                    <div class="warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير مهم:</strong> السعة تقترب من الامتلاء (${percentage}%)
                        <br>يجب البدء في أرشفة البيانات القديمة فوراً
                    </div>
                `;
            } else {
                statusDiv.innerHTML = `
                    <div class="danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>خطر!</strong> السعة ممتلئة تقريباً (${percentage}%)
                        <br>يجب حذف أو أرشفة البيانات فوراً لتجنب فقدان البيانات الجديدة
                    </div>
                `;
            }
        }

        function loadScenario(customers, cards, transmission) {
            document.getElementById('customers-count').value = customers;
            document.getElementById('cards-count').value = cards;
            document.getElementById('transmission-count').value = transmission;
            calculateCapacity();
        }

        // حساب أولي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            calculateCapacity();
        });
    </script>
</body>
</html>
