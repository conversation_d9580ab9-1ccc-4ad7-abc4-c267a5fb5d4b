<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقب التخزين - مؤسسة وقود المستقبل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
            border-radius: 10px;
        }
        .storage-bar {
            width: 100%;
            height: 30px;
            background: #e0e0e0;
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
            position: relative;
        }
        .storage-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #ff9800, #f44336);
            transition: width 0.3s ease;
            border-radius: 15px;
        }
        .storage-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2196f3;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .warning {
            background: #fff3e0;
            border-left-color: #ff9800;
        }
        .warning .stat-number {
            color: #ff9800;
        }
        .danger {
            background: #ffebee;
            border-left-color: #f44336;
        }
        .danger .stat-number {
            color: #f44336;
        }
        .btn {
            background: #2196f3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #1976d2;
        }
        .btn.warning {
            background: #ff9800;
        }
        .btn.danger {
            background: #f44336;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .details-table th, .details-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        .details-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .alert.warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .alert.danger {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .alert.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-hdd"></i> مراقب التخزين</h1>
            <p>مراقبة استهلاك مساحة التخزين المحلي</p>
        </div>

        <!-- شريط التخزين -->
        <div>
            <h3>استهلاك مساحة التخزين</h3>
            <div class="storage-bar">
                <div class="storage-fill" id="storage-fill"></div>
                <div class="storage-text" id="storage-text">فحص...</div>
            </div>
        </div>

        <!-- إحصائيات التخزين -->
        <div class="stats-grid" id="storage-stats"></div>

        <!-- تحذيرات -->
        <div id="storage-alerts"></div>

        <!-- تفاصيل التخزين -->
        <div>
            <h3>تفاصيل استهلاك التخزين</h3>
            <table class="details-table" id="storage-details">
                <thead>
                    <tr>
                        <th>نوع البيانات</th>
                        <th>الحجم (بايت)</th>
                        <th>الحجم (KB)</th>
                        <th>النسبة المئوية</th>
                        <th>عدد السجلات</th>
                    </tr>
                </thead>
                <tbody id="storage-details-body">
                </tbody>
            </table>
        </div>

        <!-- أدوات الإدارة -->
        <div style="margin: 30px 0;">
            <h3>أدوات إدارة التخزين</h3>
            <button class="btn" onclick="refreshStorage()">
                <i class="fas fa-sync"></i> تحديث البيانات
            </button>
            <button class="btn warning" onclick="cleanOldData()">
                <i class="fas fa-broom"></i> تنظيف البيانات القديمة
            </button>
            <button class="btn warning" onclick="compressData()">
                <i class="fas fa-compress"></i> ضغط البيانات
            </button>
            <button class="btn danger" onclick="clearAllData()">
                <i class="fas fa-trash"></i> مسح جميع البيانات
            </button>
            <button class="btn" onclick="exportData()">
                <i class="fas fa-download"></i> تصدير البيانات
            </button>
        </div>
    </div>

    <script>
        // الحد الأقصى المقدر للتخزين (5 MB)
        const MAX_STORAGE_SIZE = 5 * 1024 * 1024; // 5 MB بالبايت

        // فحص استهلاك التخزين
        function checkStorageUsage() {
            const storageData = {};
            let totalSize = 0;

            // فحص كل عنصر في localStorage
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                const size = new Blob([value]).size;
                
                storageData[key] = {
                    size: size,
                    sizeKB: (size / 1024).toFixed(2),
                    recordCount: getRecordCount(key, value)
                };
                
                totalSize += size;
            }

            return {
                total: totalSize,
                totalKB: (totalSize / 1024).toFixed(2),
                totalMB: (totalSize / 1024 / 1024).toFixed(2),
                percentage: ((totalSize / MAX_STORAGE_SIZE) * 100).toFixed(1),
                items: storageData
            };
        }

        // حساب عدد السجلات
        function getRecordCount(key, value) {
            try {
                const data = JSON.parse(value);
                if (Array.isArray(data)) {
                    return data.length;
                } else if (typeof data === 'object' && data !== null) {
                    return Object.keys(data).length;
                }
                return 1;
            } catch (e) {
                return 1;
            }
        }

        // تحديث عرض التخزين
        function updateStorageDisplay() {
            const usage = checkStorageUsage();
            
            // تحديث شريط التخزين
            const fillElement = document.getElementById('storage-fill');
            const textElement = document.getElementById('storage-text');
            
            fillElement.style.width = Math.min(usage.percentage, 100) + '%';
            textElement.textContent = `${usage.totalMB} MB / 5 MB (${usage.percentage}%)`;

            // تحديث الإحصائيات
            updateStorageStats(usage);
            
            // تحديث التحذيرات
            updateStorageAlerts(usage);
            
            // تحديث تفاصيل التخزين
            updateStorageDetails(usage);
        }

        // تحديث إحصائيات التخزين
        function updateStorageStats(usage) {
            const statsContainer = document.getElementById('storage-stats');
            
            const remainingMB = (5 - parseFloat(usage.totalMB)).toFixed(2);
            const remainingPercentage = (100 - parseFloat(usage.percentage)).toFixed(1);
            
            let usedClass = '';
            let remainingClass = '';
            
            if (usage.percentage > 80) {
                usedClass = 'danger';
                remainingClass = 'danger';
            } else if (usage.percentage > 60) {
                usedClass = 'warning';
                remainingClass = 'warning';
            }

            statsContainer.innerHTML = `
                <div class="stat-card ${usedClass}">
                    <div class="stat-number">${usage.totalMB} MB</div>
                    <div class="stat-label">المساحة المستخدمة</div>
                </div>
                <div class="stat-card ${remainingClass}">
                    <div class="stat-number">${remainingMB} MB</div>
                    <div class="stat-label">المساحة المتبقية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${usage.percentage}%</div>
                    <div class="stat-label">نسبة الاستخدام</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${Object.keys(usage.items).length}</div>
                    <div class="stat-label">عدد أنواع البيانات</div>
                </div>
            `;
        }

        // تحديث التحذيرات
        function updateStorageAlerts(usage) {
            const alertsContainer = document.getElementById('storage-alerts');
            let alerts = '';

            if (usage.percentage > 90) {
                alerts += `
                    <div class="alert danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير خطير:</strong> مساحة التخزين ممتلئة تقريباً (${usage.percentage}%)! 
                        يجب حذف بعض البيانات فوراً لتجنب فقدان البيانات الجديدة.
                    </div>
                `;
            } else if (usage.percentage > 75) {
                alerts += `
                    <div class="alert warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>تحذير:</strong> مساحة التخزين تقترب من الامتلاء (${usage.percentage}%). 
                        يُنصح بتنظيف البيانات القديمة قريباً.
                    </div>
                `;
            } else if (usage.percentage > 50) {
                alerts += `
                    <div class="alert warning">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> تم استخدام أكثر من نصف مساحة التخزين (${usage.percentage}%). 
                        راقب الاستهلاك بانتظام.
                    </div>
                `;
            } else {
                alerts += `
                    <div class="alert success">
                        <i class="fas fa-check-circle"></i>
                        <strong>ممتاز:</strong> مساحة التخزين في حالة جيدة (${usage.percentage}% مستخدمة).
                    </div>
                `;
            }

            alertsContainer.innerHTML = alerts;
        }

        // تحديث تفاصيل التخزين
        function updateStorageDetails(usage) {
            const tbody = document.getElementById('storage-details-body');
            let rows = '';

            // ترتيب البيانات حسب الحجم
            const sortedItems = Object.entries(usage.items).sort((a, b) => b[1].size - a[1].size);

            sortedItems.forEach(([key, data]) => {
                const percentage = ((data.size / usage.total) * 100).toFixed(1);
                const displayName = getDisplayName(key);
                
                rows += `
                    <tr>
                        <td>${displayName}</td>
                        <td>${data.size.toLocaleString()}</td>
                        <td>${data.sizeKB}</td>
                        <td>${percentage}%</td>
                        <td>${data.recordCount}</td>
                    </tr>
                `;
            });

            tbody.innerHTML = rows;
        }

        // الحصول على اسم عرض مناسب
        function getDisplayName(key) {
            const names = {
                'transmissionTableData': 'جدول الإرسال',
                'gasShopData': 'البيانات الرئيسية',
                'appConfig': 'إعدادات التطبيق',
                'userPreferences': 'تفضيلات المستخدم',
                'backupData': 'النسخ الاحتياطية'
            };
            return names[key] || key;
        }

        // تنظيف البيانات القديمة
        function cleanOldData() {
            if (confirm('هل تريد تنظيف البيانات القديمة؟ سيتم الاحتفاظ بآخر 1000 سجل فقط.')) {
                try {
                    // تنظيف جدول الإرسال
                    const transmissionData = JSON.parse(localStorage.getItem('transmissionTableData') || '[]');
                    if (transmissionData.length > 1000) {
                        const cleanedData = transmissionData.slice(-1000);
                        localStorage.setItem('transmissionTableData', JSON.stringify(cleanedData));
                    }

                    alert('تم تنظيف البيانات القديمة بنجاح!');
                    refreshStorage();
                } catch (error) {
                    alert('خطأ في تنظيف البيانات: ' + error.message);
                }
            }
        }

        // ضغط البيانات
        function compressData() {
            if (confirm('هل تريد ضغط البيانات؟ سيتم إزالة المسافات الزائدة.')) {
                try {
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        const value = localStorage.getItem(key);
                        
                        try {
                            const data = JSON.parse(value);
                            const compressedValue = JSON.stringify(data);
                            localStorage.setItem(key, compressedValue);
                        } catch (e) {
                            // تجاهل البيانات التي ليست JSON
                        }
                    }

                    alert('تم ضغط البيانات بنجاح!');
                    refreshStorage();
                } catch (error) {
                    alert('خطأ في ضغط البيانات: ' + error.message);
                }
            }
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                if (confirm('تأكيد أخير: سيتم مسح جميع البيانات نهائياً!')) {
                    localStorage.clear();
                    alert('تم مسح جميع البيانات!');
                    refreshStorage();
                }
            }
        }

        // تصدير البيانات
        function exportData() {
            try {
                const allData = {};
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    allData[key] = localStorage.getItem(key);
                }

                const dataStr = JSON.stringify(allData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `storage-backup-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                alert('تم تصدير البيانات بنجاح!');
            } catch (error) {
                alert('خطأ في تصدير البيانات: ' + error.message);
            }
        }

        // تحديث البيانات
        function refreshStorage() {
            updateStorageDisplay();
        }

        // تشغيل الفحص عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStorageDisplay();
            
            // تحديث تلقائي كل 10 ثوانٍ
            setInterval(updateStorageDisplay, 10000);
        });
    </script>
</body>
</html>
