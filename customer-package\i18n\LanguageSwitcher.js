/**
 * مكون تبديل اللغة
 * Composant de Commutation de Langue
 * مؤسسة وقود المستقبل - Future Fuel Corporation
 */

class LanguageSwitcher {
    constructor(containerId = 'language-switcher') {
        this.containerId = containerId;
        this.container = null;
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * تهيئة مكون تبديل اللغة
     * Initialiser le composant de commutation de langue
     */
    init() {
        // البحث عن الحاوية أو إنشاؤها
        this.container = document.getElementById(this.containerId);
        
        if (!this.container) {
            this.createContainer();
        }
        
        this.render();
        this.attachEventListeners();
        this.isInitialized = true;
        
        console.log('✅ تم تهيئة مكون تبديل اللغة');
        console.log('✅ Composant de commutation de langue initialisé');
    }

    /**
     * إنشاء حاوية تبديل اللغة
     * Créer le conteneur de commutation de langue
     */
    createContainer() {
        this.container = document.createElement('div');
        this.container.id = this.containerId;
        this.container.className = 'language-switcher-container';
        
        // إضافة إلى الصفحة (في الزاوية العلوية)
        document.body.appendChild(this.container);
        
        // إضافة الأنماط
        this.addStyles();
    }

    /**
     * إضافة الأنماط
     * Ajouter les styles
     */
    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .language-switcher-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                background: white;
                border-radius: 25px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                padding: 5px;
                display: flex;
                align-items: center;
                gap: 5px;
                font-family: Arial, sans-serif;
                transition: all 0.3s ease;
            }

            .language-switcher-container:hover {
                box-shadow: 0 6px 20px rgba(0,0,0,0.2);
                transform: translateY(-2px);
            }

            .language-option {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 15px;
                border-radius: 20px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 14px;
                font-weight: 500;
                text-decoration: none;
                color: #666;
                border: 2px solid transparent;
            }

            .language-option:hover {
                background: #f5f5f5;
                color: #333;
            }

            .language-option.active {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-color: #667eea;
                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            }

            .language-flag {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                background: #f0f0f0;
                border: 1px solid #ddd;
            }

            .language-option.active .language-flag {
                background: rgba(255,255,255,0.2);
                border-color: rgba(255,255,255,0.3);
            }

            .language-text {
                font-weight: 600;
                letter-spacing: 0.5px;
            }

            /* تأثيرات الانتقال */
            .language-switcher-container.switching {
                transform: scale(1.05);
            }

            /* للشاشات الصغيرة */
            @media (max-width: 768px) {
                .language-switcher-container {
                    top: 10px;
                    right: 10px;
                    padding: 3px;
                }
                
                .language-option {
                    padding: 6px 10px;
                    font-size: 12px;
                }
                
                .language-flag {
                    width: 16px;
                    height: 16px;
                    font-size: 10px;
                }
                
                .language-text {
                    display: none;
                }
            }

            /* تأثير النبضة للغة النشطة */
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }

            .language-option.active:hover {
                animation: pulse 0.6s ease-in-out;
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * رسم المكون
     * Rendre le composant
     */
    render() {
        const currentLang = window.i18n.getCurrentLanguage();
        
        this.container.innerHTML = `
            <div class="language-option ${currentLang === 'ar' ? 'active' : ''}" data-lang="ar">
                <div class="language-flag">🇸🇦</div>
                <span class="language-text">العربية</span>
            </div>
            <div class="language-option ${currentLang === 'fr' ? 'active' : ''}" data-lang="fr">
                <div class="language-flag">🇫🇷</div>
                <span class="language-text">Français</span>
            </div>
        `;
    }

    /**
     * ربط أحداث النقر
     * Attacher les événements de clic
     */
    attachEventListeners() {
        const options = this.container.querySelectorAll('.language-option');
        
        options.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                const selectedLang = option.getAttribute('data-lang');
                this.switchLanguage(selectedLang);
            });
        });

        // الاستماع لتغيير اللغة من مصادر أخرى
        window.addEventListener('languageChanged', (e) => {
            this.updateActiveState(e.detail.language);
        });
    }

    /**
     * تبديل اللغة
     * Changer la langue
     */
    switchLanguage(language) {
        // إضافة تأثير الانتقال
        this.container.classList.add('switching');
        
        setTimeout(() => {
            // تغيير اللغة
            const success = window.i18n.setLanguage(language);
            
            if (success) {
                this.updateActiveState(language);
                this.showSwitchNotification(language);
            }
            
            // إزالة تأثير الانتقال
            this.container.classList.remove('switching');
        }, 150);
    }

    /**
     * تحديث الحالة النشطة
     * Mettre à jour l'état actif
     */
    updateActiveState(language) {
        const options = this.container.querySelectorAll('.language-option');
        
        options.forEach(option => {
            const optionLang = option.getAttribute('data-lang');
            
            if (optionLang === language) {
                option.classList.add('active');
            } else {
                option.classList.remove('active');
            }
        });
    }

    /**
     * إظهار إشعار التبديل
     * Afficher la notification de commutation
     */
    showSwitchNotification(language) {
        const messages = {
            'ar': 'تم تغيير اللغة إلى العربية',
            'fr': 'Langue changée vers le français'
        };
        
        const notification = document.createElement('div');
        notification.className = 'language-switch-notification';
        notification.textContent = messages[language];
        
        // أنماط الإشعار
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: 500;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // إظهار الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // إخفاء الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 2000);
    }

    /**
     * تحديث موقع المكون
     * Mettre à jour la position du composant
     */
    setPosition(position = 'top-right') {
        const positions = {
            'top-right': { top: '20px', right: '20px', left: 'auto', bottom: 'auto' },
            'top-left': { top: '20px', left: '20px', right: 'auto', bottom: 'auto' },
            'bottom-right': { bottom: '20px', right: '20px', top: 'auto', left: 'auto' },
            'bottom-left': { bottom: '20px', left: '20px', top: 'auto', right: 'auto' }
        };
        
        const pos = positions[position] || positions['top-right'];
        
        Object.assign(this.container.style, pos);
    }

    /**
     * إخفاء/إظهار المكون
     * Masquer/Afficher le composant
     */
    toggle(show = null) {
        if (show === null) {
            show = this.container.style.display === 'none';
        }
        
        this.container.style.display = show ? 'flex' : 'none';
    }

    /**
     * تدمير المكون
     * Détruire le composant
     */
    destroy() {
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        
        this.isInitialized = false;
        
        console.log('تم تدمير مكون تبديل اللغة');
        console.log('Composant de commutation de langue détruit');
    }
}

// دالة مساعدة لإنشاء مبدل اللغة
window.createLanguageSwitcher = function(containerId, position = 'top-right') {
    const switcher = new LanguageSwitcher(containerId);
    switcher.setPosition(position);
    return switcher;
};

// إنشاء مبدل اللغة تلقائياً عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود نظام اللغات
    if (window.i18n) {
        window.languageSwitcher = new LanguageSwitcher();
        console.log('✅ تم إنشاء مبدل اللغة تلقائياً');
        console.log('✅ Commutateur de langue créé automatiquement');
    } else {
        console.warn('⚠️ نظام اللغات غير متاح - يجب تحميل LanguageManager.js أولاً');
        console.warn('⚠️ Système de langues non disponible - LanguageManager.js doit être chargé en premier');
    }
});

console.log('✅ تم تحميل مكون تبديل اللغة');
console.log('✅ Composant de commutation de langue chargé');
