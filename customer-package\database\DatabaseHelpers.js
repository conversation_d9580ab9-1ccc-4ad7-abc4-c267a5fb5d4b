/**
 * وظائف مساعدة لقاعدة البيانات
 * Database Helper Functions
 * مؤسسة وقود المستقبل - Future Fuel Corporation
 */

// إضافة الوظائف المساعدة لكلاس DatabaseManager
DatabaseManager.prototype.validateData = function(tableName, data, isUpdate = false) {
    const schema = this.tables[tableName].schema;
    
    Object.keys(schema).forEach(field => {
        const fieldSchema = schema[field];
        const value = data[field];
        
        // التحقق من الحقول المطلوبة
        if (fieldSchema.required && !isUpdate && (value === undefined || value === null || value === '')) {
            throw new Error(`الحقل ${field} مطلوب`);
        }
        
        // التحقق من نوع البيانات
        if (value !== undefined && value !== null) {
            this.validateFieldType(field, value, fieldSchema);
        }
        
        // التحقق من القيم الفريدة
        if (fieldSchema.unique && value !== undefined) {
            this.validateUnique(tableName, field, value, isUpdate ? data.id : null);
        }
        
        // التحقق من قيم ENUM
        if (fieldSchema.type === 'ENUM' && value !== undefined) {
            if (!fieldSchema.values.includes(value)) {
                throw new Error(`قيمة الحقل ${field} يجب أن تكون إحدى القيم: ${fieldSchema.values.join(', ')}`);
            }
        }
        
        // التحقق من الحد الأدنى والأقصى
        if (fieldSchema.min !== undefined && value < fieldSchema.min) {
            throw new Error(`قيمة الحقل ${field} يجب أن تكون أكبر من أو تساوي ${fieldSchema.min}`);
        }
        
        if (fieldSchema.max !== undefined && value > fieldSchema.max) {
            throw new Error(`قيمة الحقل ${field} يجب أن تكون أقل من أو تساوي ${fieldSchema.max}`);
        }
    });
};

DatabaseManager.prototype.validateFieldType = function(field, value, fieldSchema) {
    const type = fieldSchema.type;
    
    switch (type) {
        case 'INTEGER':
            if (!Number.isInteger(Number(value))) {
                throw new Error(`الحقل ${field} يجب أن يكون رقماً صحيحاً`);
            }
            break;
            
        case 'DECIMAL':
            if (isNaN(Number(value))) {
                throw new Error(`الحقل ${field} يجب أن يكون رقماً`);
            }
            break;
            
        case 'VARCHAR':
            if (typeof value !== 'string') {
                throw new Error(`الحقل ${field} يجب أن يكون نصاً`);
            }
            // استخراج الطول الأقصى من VARCHAR(n)
            const maxLength = parseInt(type.match(/\d+/)?.[0] || '255');
            if (value.length > maxLength) {
                throw new Error(`الحقل ${field} يجب أن يكون أقل من ${maxLength} حرف`);
            }
            break;
            
        case 'DATE':
            if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
                throw new Error(`الحقل ${field} يجب أن يكون بصيغة YYYY-MM-DD`);
            }
            break;
            
        case 'DATETIME':
            if (!/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
                throw new Error(`الحقل ${field} يجب أن يكون بصيغة تاريخ ووقت صحيحة`);
            }
            break;
            
        case 'TIME':
            if (!/^\d{2}:\d{2}:\d{2}$/.test(value)) {
                throw new Error(`الحقل ${field} يجب أن يكون بصيغة HH:MM:SS`);
            }
            break;
            
        case 'BOOLEAN':
            if (typeof value !== 'boolean') {
                throw new Error(`الحقل ${field} يجب أن يكون true أو false`);
            }
            break;
            
        case 'JSON':
            try {
                if (typeof value === 'string') {
                    JSON.parse(value);
                }
            } catch (e) {
                throw new Error(`الحقل ${field} يجب أن يكون JSON صحيح`);
            }
            break;
    }
};

DatabaseManager.prototype.validateUnique = function(tableName, field, value, excludeId = null) {
    const table = this.tables[tableName];
    const primaryKey = this.getPrimaryKey(tableName);
    
    const exists = table.data.some(record => {
        if (excludeId && record[primaryKey] === excludeId) {
            return false; // تجاهل السجل الحالي عند التحديث
        }
        return record[field] === value;
    });
    
    if (exists) {
        throw new Error(`قيمة الحقل ${field} موجودة مسبقاً`);
    }
};

DatabaseManager.prototype.getPrimaryKey = function(tableName) {
    const schema = this.tables[tableName].schema;
    return Object.keys(schema).find(field => schema[field].primaryKey) || 'id';
};

DatabaseManager.prototype.getNextId = function(tableName) {
    const table = this.tables[tableName];
    const primaryKey = this.getPrimaryKey(tableName);
    
    if (table.data.length === 0) {
        return 1;
    }
    
    const maxId = Math.max(...table.data.map(record => record[primaryKey] || 0));
    return maxId + 1;
};

DatabaseManager.prototype.setDefaultValues = function(tableName, record) {
    const schema = this.tables[tableName].schema;
    
    Object.keys(schema).forEach(field => {
        const fieldSchema = schema[field];
        
        if (record[field] === undefined && fieldSchema.default !== undefined) {
            if (fieldSchema.default === 'NOW()') {
                if (fieldSchema.type === 'DATE') {
                    record[field] = new Date().toISOString().split('T')[0];
                } else if (fieldSchema.type === 'DATETIME') {
                    record[field] = new Date().toISOString();
                } else if (fieldSchema.type === 'TIME') {
                    record[field] = new Date().toTimeString().split(' ')[0];
                }
            } else {
                record[field] = fieldSchema.default;
            }
        }
    });
};

DatabaseManager.prototype.executeTriggers = function(tableName, event, record) {
    Object.keys(this.triggers).forEach(triggerName => {
        const trigger = this.triggers[triggerName];
        
        if (trigger.table === tableName || (trigger.tables && trigger.tables.includes(tableName))) {
            if (trigger.event === event) {
                try {
                    trigger.action(record);
                } catch (error) {
                    console.error(`خطأ في تنفيذ المحفز ${triggerName}:`, error);
                }
            }
        }
    });
};

DatabaseManager.prototype.updateCustomerBalance = function(customerId, amount) {
    try {
        const customer = this.findOne('customers', { id: customerId });
        if (customer) {
            const newBalance = (customer.currentBalance || 0) + amount;
            this.update('customers', customerId, { currentBalance: newBalance });
        }
    } catch (error) {
        console.error('خطأ في تحديث رصيد العميل:', error);
    }
};

DatabaseManager.prototype.updateProductStock = function(productId, quantity) {
    try {
        const product = this.findOne('products', { id: productId });
        if (product) {
            const newStock = (product.currentStock || 0) + quantity;
            this.update('products', productId, { currentStock: newStock });
        }
    } catch (error) {
        console.error('خطأ في تحديث مخزون المنتج:', error);
    }
};

// وظائف الاستعلامات المتقدمة
DatabaseManager.prototype.join = function(mainTable, joinTable, joinCondition, type = 'INNER') {
    const mainData = this.tables[mainTable].data;
    const joinData = this.tables[joinTable].data;
    const results = [];
    
    mainData.forEach(mainRecord => {
        const matches = joinData.filter(joinRecord => {
            return this.evaluateJoinCondition(mainRecord, joinRecord, joinCondition);
        });
        
        if (matches.length > 0) {
            matches.forEach(match => {
                results.push({
                    ...mainRecord,
                    [`${joinTable}_data`]: match
                });
            });
        } else if (type === 'LEFT') {
            results.push({
                ...mainRecord,
                [`${joinTable}_data`]: null
            });
        }
    });
    
    return results;
};

DatabaseManager.prototype.evaluateJoinCondition = function(mainRecord, joinRecord, condition) {
    const [mainField, joinField] = condition.split('=').map(s => s.trim());
    return mainRecord[mainField] === joinRecord[joinField];
};

// وظائف التجميع
DatabaseManager.prototype.aggregate = function(tableName, pipeline) {
    let data = [...this.tables[tableName].data];
    
    pipeline.forEach(stage => {
        if (stage.$match) {
            data = data.filter(record => {
                return Object.keys(stage.$match).every(key => {
                    return record[key] === stage.$match[key];
                });
            });
        }
        
        if (stage.$group) {
            const grouped = {};
            
            data.forEach(record => {
                const groupKey = stage.$group._id ? record[stage.$group._id] : 'all';
                
                if (!grouped[groupKey]) {
                    grouped[groupKey] = {
                        _id: groupKey,
                        count: 0,
                        records: []
                    };
                }
                
                grouped[groupKey].count++;
                grouped[groupKey].records.push(record);
                
                // تطبيق عمليات التجميع
                Object.keys(stage.$group).forEach(field => {
                    if (field !== '_id') {
                        const operation = stage.$group[field];
                        
                        if (operation.$sum) {
                            if (!grouped[groupKey][field]) grouped[groupKey][field] = 0;
                            grouped[groupKey][field] += record[operation.$sum] || 0;
                        }
                        
                        if (operation.$avg) {
                            if (!grouped[groupKey][`${field}_sum`]) grouped[groupKey][`${field}_sum`] = 0;
                            grouped[groupKey][`${field}_sum`] += record[operation.$avg] || 0;
                            grouped[groupKey][field] = grouped[groupKey][`${field}_sum`] / grouped[groupKey].count;
                        }
                        
                        if (operation.$max) {
                            const value = record[operation.$max];
                            if (!grouped[groupKey][field] || value > grouped[groupKey][field]) {
                                grouped[groupKey][field] = value;
                            }
                        }
                        
                        if (operation.$min) {
                            const value = record[operation.$min];
                            if (!grouped[groupKey][field] || value < grouped[groupKey][field]) {
                                grouped[groupKey][field] = value;
                            }
                        }
                    }
                });
            });
            
            data = Object.values(grouped);
        }
        
        if (stage.$sort) {
            const sortField = Object.keys(stage.$sort)[0];
            const sortDirection = stage.$sort[sortField];
            
            data.sort((a, b) => {
                const aVal = a[sortField];
                const bVal = b[sortField];
                
                if (sortDirection === -1) {
                    return bVal > aVal ? 1 : bVal < aVal ? -1 : 0;
                } else {
                    return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
                }
            });
        }
        
        if (stage.$limit) {
            data = data.slice(0, stage.$limit);
        }
    });
    
    return data;
};

// وظائف النسخ الاحتياطي والاستعادة
DatabaseManager.prototype.backup = function() {
    const backup = {
        timestamp: new Date().toISOString(),
        version: this.version,
        tables: {}
    };
    
    Object.keys(this.tables).forEach(tableName => {
        backup.tables[tableName] = {
            schema: this.tables[tableName].schema,
            data: [...this.tables[tableName].data]
        };
    });
    
    this.backups.push(backup);
    
    // الاحتفاظ بآخر 10 نسخ احتياطية فقط
    if (this.backups.length > 10) {
        this.backups = this.backups.slice(-10);
    }
    
    return backup;
};

DatabaseManager.prototype.restore = function(backupIndex = -1) {
    if (this.backups.length === 0) {
        throw new Error('لا توجد نسخ احتياطية متاحة');
    }
    
    const backup = this.backups[backupIndex];
    if (!backup) {
        throw new Error('النسخة الاحتياطية غير موجودة');
    }
    
    // استعادة البيانات
    Object.keys(backup.tables).forEach(tableName => {
        if (this.tables[tableName]) {
            this.tables[tableName].data = [...backup.tables[tableName].data];
        }
    });
    
    // حفظ البيانات المستعادة
    this.saveData();
    
    console.log(`✅ تم استعادة النسخة الاحتياطية من ${backup.timestamp}`);
    return true;
};

// وظائف الإحصائيات
DatabaseManager.prototype.getStatistics = function() {
    const stats = {
        tables: {},
        totalRecords: 0,
        databaseSize: 0
    };
    
    Object.keys(this.tables).forEach(tableName => {
        const table = this.tables[tableName];
        const recordCount = table.data.length;
        const tableSize = JSON.stringify(table.data).length;
        
        stats.tables[tableName] = {
            recordCount: recordCount,
            size: tableSize,
            sizeKB: (tableSize / 1024).toFixed(2)
        };
        
        stats.totalRecords += recordCount;
        stats.databaseSize += tableSize;
    });
    
    stats.databaseSizeKB = (stats.databaseSize / 1024).toFixed(2);
    stats.databaseSizeMB = (stats.databaseSize / 1024 / 1024).toFixed(2);
    
    return stats;
};

console.log('✅ تم تحميل الوظائف المساعدة لقاعدة البيانات');
