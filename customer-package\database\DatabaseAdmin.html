<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة قاعدة البيانات - مؤسسة وقود المستقبل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
            margin: 0;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #673ab7, #3f51b5);
            color: white;
            border-radius: 10px;
        }
        .tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 15px 25px;
            background: #f5f5f5;
            border: none;
            cursor: pointer;
            border-radius: 5px 5px 0 0;
            margin-left: 5px;
            font-size: 14px;
        }
        .tab.active {
            background: #673ab7;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 5px;
            background: #fafafa;
        }
        .tab-content.active {
            display: block;
        }
        .table-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .table-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .table-card h3 {
            margin-top: 0;
            color: #673ab7;
            border-bottom: 2px solid #673ab7;
            padding-bottom: 10px;
        }
        .table-info {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .table-info-value {
            font-weight: bold;
            color: #673ab7;
        }
        .btn {
            background: #673ab7;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        .btn:hover {
            background: #5e35b1;
        }
        .btn.success {
            background: #4caf50;
        }
        .btn.warning {
            background: #ff9800;
        }
        .btn.danger {
            background: #f44336;
        }
        .btn.info {
            background: #2196f3;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            font-size: 12px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        .data-table th {
            background: #673ab7;
            color: white;
            font-weight: bold;
        }
        .data-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .query-area {
            width: 100%;
            height: 150px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            direction: ltr;
            text-align: left;
        }
        .result-area {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            direction: ltr;
            text-align: left;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .alert.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .alert.warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .alert.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .alert.info {
            background: #e3f2fd;
            color: #1565c0;
            border: 1px solid #2196f3;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #673ab7;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #673ab7;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-database"></i> إدارة قاعدة البيانات الاحترافية</h1>
            <p>نظام إدارة شامل لقاعدة بيانات مؤسسة وقود المستقبل</p>
        </div>

        <!-- التبويبات -->
        <div class="tabs">
            <button class="tab active" onclick="showTab('overview')">نظرة عامة</button>
            <button class="tab" onclick="showTab('tables')">الجداول</button>
            <button class="tab" onclick="showTab('query')">الاستعلامات</button>
            <button class="tab" onclick="showTab('data-entry')">إدخال البيانات</button>
            <button class="tab" onclick="showTab('backup')">النسخ الاحتياطي</button>
            <button class="tab" onclick="showTab('maintenance')">الصيانة</button>
        </div>

        <!-- نظرة عامة -->
        <div id="overview" class="tab-content active">
            <h3>إحصائيات قاعدة البيانات</h3>
            <div class="stats-grid" id="database-stats"></div>
            
            <h3>حالة الجداول</h3>
            <div class="table-grid" id="tables-overview"></div>
            
            <div style="margin: 20px 0;">
                <button class="btn success" onclick="initializeDatabase()">
                    <i class="fas fa-play"></i> تهيئة قاعدة البيانات
                </button>
                <button class="btn info" onclick="refreshOverview()">
                    <i class="fas fa-sync"></i> تحديث البيانات
                </button>
                <button class="btn warning" onclick="createSampleData()">
                    <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
                </button>
            </div>
        </div>

        <!-- الجداول -->
        <div id="tables" class="tab-content">
            <h3>إدارة الجداول</h3>
            <div class="form-group">
                <label>اختر جدول:</label>
                <select id="table-selector" onchange="loadTableData()">
                    <option value="">-- اختر جدول --</option>
                </select>
            </div>
            
            <div id="table-data-container"></div>
        </div>

        <!-- الاستعلامات -->
        <div id="query" class="tab-content">
            <h3>منشئ الاستعلامات</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label>الجدول:</label>
                    <select id="query-table">
                        <option value="">-- اختر جدول --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>نوع الاستعلام:</label>
                    <select id="query-type">
                        <option value="select">استعلام (SELECT)</option>
                        <option value="insert">إدراج (INSERT)</option>
                        <option value="update">تحديث (UPDATE)</option>
                        <option value="delete">حذف (DELETE)</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label>الاستعلام المخصص:</label>
                <textarea id="custom-query" class="query-area" placeholder="أدخل استعلامك هنا..."></textarea>
            </div>
            
            <div style="margin: 15px 0;">
                <button class="btn success" onclick="executeQuery()">
                    <i class="fas fa-play"></i> تنفيذ الاستعلام
                </button>
                <button class="btn info" onclick="generateQuery()">
                    <i class="fas fa-magic"></i> إنشاء استعلام تلقائي
                </button>
                <button class="btn" onclick="clearQuery()">
                    <i class="fas fa-eraser"></i> مسح
                </button>
            </div>
            
            <div class="form-group">
                <label>النتائج:</label>
                <div id="query-results" class="result-area"></div>
            </div>
        </div>

        <!-- إدخال البيانات -->
        <div id="data-entry" class="tab-content">
            <h3>إدخال البيانات</h3>
            
            <div class="form-group">
                <label>اختر جدول:</label>
                <select id="entry-table" onchange="generateEntryForm()">
                    <option value="">-- اختر جدول --</option>
                </select>
            </div>
            
            <div id="entry-form-container"></div>
        </div>

        <!-- النسخ الاحتياطي -->
        <div id="backup" class="tab-content">
            <h3>إدارة النسخ الاحتياطية</h3>
            
            <div style="margin: 20px 0;">
                <button class="btn success" onclick="createBackup()">
                    <i class="fas fa-save"></i> إنشاء نسخة احتياطية
                </button>
                <button class="btn info" onclick="exportDatabase()">
                    <i class="fas fa-download"></i> تصدير قاعدة البيانات
                </button>
                <button class="btn warning" onclick="importDatabase()">
                    <i class="fas fa-upload"></i> استيراد قاعدة البيانات
                </button>
            </div>
            
            <div id="backup-list"></div>
        </div>

        <!-- الصيانة -->
        <div id="maintenance" class="tab-content">
            <h3>صيانة قاعدة البيانات</h3>
            
            <div style="margin: 20px 0;">
                <button class="btn info" onclick="optimizeDatabase()">
                    <i class="fas fa-tools"></i> تحسين قاعدة البيانات
                </button>
                <button class="btn warning" onclick="validateDatabase()">
                    <i class="fas fa-check-circle"></i> التحقق من سلامة البيانات
                </button>
                <button class="btn warning" onclick="cleanupDatabase()">
                    <i class="fas fa-broom"></i> تنظيف البيانات
                </button>
                <button class="btn danger" onclick="resetDatabase()">
                    <i class="fas fa-exclamation-triangle"></i> إعادة تعيين قاعدة البيانات
                </button>
            </div>
            
            <div id="maintenance-results"></div>
        </div>
    </div>

    <!-- تحميل ملفات قاعدة البيانات -->
    <script src="DatabaseManager.js"></script>
    <script src="DatabaseHelpers.js"></script>
    <script>
        // متغير قاعدة البيانات العامة
        let db = null;
        
        // تهيئة قاعدة البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeDatabase();
            populateTableSelectors();
            refreshOverview();
        });

        // تهيئة قاعدة البيانات
        function initializeDatabase() {
            try {
                db = new DatabaseManager();
                showAlert('تم تهيئة قاعدة البيانات بنجاح', 'success');
                refreshOverview();
            } catch (error) {
                showAlert('خطأ في تهيئة قاعدة البيانات: ' + error.message, 'error');
            }
        }

        // عرض التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // تحديث النظرة العامة
        function refreshOverview() {
            if (!db) return;
            
            try {
                const stats = db.getStatistics();
                updateDatabaseStats(stats);
                updateTablesOverview();
            } catch (error) {
                showAlert('خطأ في تحديث البيانات: ' + error.message, 'error');
            }
        }

        // تحديث إحصائيات قاعدة البيانات
        function updateDatabaseStats(stats) {
            const container = document.getElementById('database-stats');
            
            container.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${Object.keys(stats.tables).length}</div>
                    <div class="stat-label">عدد الجداول</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.totalRecords}</div>
                    <div class="stat-label">إجمالي السجلات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${stats.databaseSizeKB} KB</div>
                    <div class="stat-label">حجم قاعدة البيانات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${db.backups.length}</div>
                    <div class="stat-label">النسخ الاحتياطية</div>
                </div>
            `;
        }

        // تحديث نظرة عامة على الجداول
        function updateTablesOverview() {
            const container = document.getElementById('tables-overview');
            const stats = db.getStatistics();
            
            let html = '';
            Object.keys(stats.tables).forEach(tableName => {
                const tableStats = stats.tables[tableName];
                const displayName = getTableDisplayName(tableName);
                
                html += `
                    <div class="table-card">
                        <h3>${displayName}</h3>
                        <div class="table-info">
                            <span>عدد السجلات:</span>
                            <span class="table-info-value">${tableStats.recordCount}</span>
                        </div>
                        <div class="table-info">
                            <span>الحجم:</span>
                            <span class="table-info-value">${tableStats.sizeKB} KB</span>
                        </div>
                        <div style="margin-top: 15px;">
                            <button class="btn info" onclick="viewTable('${tableName}')">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                            <button class="btn success" onclick="addRecord('${tableName}')">
                                <i class="fas fa-plus"></i> إضافة
                            </button>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // الحصول على اسم الجدول للعرض
        function getTableDisplayName(tableName) {
            const names = {
                'customers': 'العملاء',
                'gasCards': 'بطاقات الغاز',
                'transmissionTable': 'جدول الإرسال',
                'employees': 'الموظفين',
                'products': 'المنتجات',
                'suppliers': 'الموردين',
                'sales': 'المبيعات',
                'saleItems': 'تفاصيل المبيعات',
                'purchases': 'المشتريات',
                'appointments': 'المواعيد',
                'transactions': 'المعاملات المالية'
            };
            return names[tableName] || tableName;
        }

        // ملء قوائم اختيار الجداول
        function populateTableSelectors() {
            if (!db) return;
            
            const selectors = ['table-selector', 'query-table', 'entry-table'];
            const tableNames = Object.keys(db.tables);
            
            selectors.forEach(selectorId => {
                const selector = document.getElementById(selectorId);
                selector.innerHTML = '<option value="">-- اختر جدول --</option>';
                
                tableNames.forEach(tableName => {
                    const displayName = getTableDisplayName(tableName);
                    selector.innerHTML += `<option value="${tableName}">${displayName}</option>`;
                });
            });
        }

        // عرض رسالة
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${type}`;
            alertDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
            
            // إدراج الرسالة في أعلى الحاوية
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild.nextSibling);
            
            // إزالة الرسالة بعد 5 ثوانٍ
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // عرض جدول
        function viewTable(tableName) {
            showTab('tables');
            document.getElementById('table-selector').value = tableName;
            loadTableData();
        }

        // تحميل بيانات الجدول
        function loadTableData() {
            const tableName = document.getElementById('table-selector').value;
            if (!tableName || !db) return;

            const container = document.getElementById('table-data-container');
            const data = db.select(tableName);
            const schema = db.tables[tableName].schema;

            if (data.length === 0) {
                container.innerHTML = `
                    <div class="alert info">
                        <i class="fas fa-info-circle"></i> لا توجد بيانات في جدول ${getTableDisplayName(tableName)}
                    </div>
                `;
                return;
            }

            // إنشاء جدول البيانات
            let html = `
                <h4>${getTableDisplayName(tableName)} (${data.length} سجل)</h4>
                <div style="margin: 15px 0;">
                    <button class="btn success" onclick="addRecord('${tableName}')">
                        <i class="fas fa-plus"></i> إضافة سجل جديد
                    </button>
                    <button class="btn warning" onclick="exportTable('${tableName}')">
                        <i class="fas fa-download"></i> تصدير الجدول
                    </button>
                </div>
                <div style="overflow-x: auto;">
                    <table class="data-table">
                        <thead>
                            <tr>
            `;

            // رؤوس الأعمدة
            Object.keys(schema).forEach(field => {
                html += `<th>${field}</th>`;
            });
            html += '<th>الإجراءات</th></tr></thead><tbody>';

            // صفوف البيانات
            data.forEach(record => {
                html += '<tr>';
                Object.keys(schema).forEach(field => {
                    let value = record[field];
                    if (value === null || value === undefined) value = '';
                    if (typeof value === 'object') value = JSON.stringify(value);
                    html += `<td>${value}</td>`;
                });

                const primaryKey = db.getPrimaryKey(tableName);
                const id = record[primaryKey];
                html += `
                    <td>
                        <button class="btn info" onclick="editRecord('${tableName}', ${id})" style="padding: 4px 8px; font-size: 10px;">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn danger" onclick="deleteRecord('${tableName}', ${id})" style="padding: 4px 8px; font-size: 10px;">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                html += '</tr>';
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // إضافة سجل جديد
        function addRecord(tableName) {
            showTab('data-entry');
            document.getElementById('entry-table').value = tableName;
            generateEntryForm();
        }

        // إنشاء نموذج إدخال البيانات
        function generateEntryForm() {
            const tableName = document.getElementById('entry-table').value;
            if (!tableName || !db) return;

            const container = document.getElementById('entry-form-container');
            const schema = db.tables[tableName].schema;

            let html = `
                <h4>إضافة سجل جديد - ${getTableDisplayName(tableName)}</h4>
                <form id="entry-form" onsubmit="submitRecord(event, '${tableName}')">
                    <div class="form-row">
            `;

            Object.keys(schema).forEach(field => {
                const fieldSchema = schema[field];

                // تجاهل المفاتيح الأساسية التلقائية
                if (fieldSchema.primaryKey && fieldSchema.autoIncrement) {
                    return;
                }

                html += `<div class="form-group">`;
                html += `<label>${field} ${fieldSchema.required ? '*' : ''}</label>`;

                if (fieldSchema.type === 'ENUM') {
                    html += `<select name="${field}" ${fieldSchema.required ? 'required' : ''}>`;
                    html += `<option value="">-- اختر --</option>`;
                    fieldSchema.values.forEach(value => {
                        const selected = value === fieldSchema.default ? 'selected' : '';
                        html += `<option value="${value}" ${selected}>${value}</option>`;
                    });
                    html += `</select>`;
                } else if (fieldSchema.type === 'TEXT') {
                    html += `<textarea name="${field}" ${fieldSchema.required ? 'required' : ''}></textarea>`;
                } else if (fieldSchema.type === 'BOOLEAN') {
                    html += `<select name="${field}">`;
                    html += `<option value="true">نعم</option>`;
                    html += `<option value="false">لا</option>`;
                    html += `</select>`;
                } else {
                    let inputType = 'text';
                    if (fieldSchema.type === 'DATE') inputType = 'date';
                    else if (fieldSchema.type === 'TIME') inputType = 'time';
                    else if (fieldSchema.type === 'DATETIME') inputType = 'datetime-local';
                    else if (fieldSchema.type === 'INTEGER' || fieldSchema.type === 'DECIMAL') inputType = 'number';

                    html += `<input type="${inputType}" name="${field}" ${fieldSchema.required ? 'required' : ''}>`;
                }

                html += `</div>`;
            });

            html += `
                    </div>
                    <div style="margin: 20px 0;">
                        <button type="submit" class="btn success">
                            <i class="fas fa-save"></i> حفظ السجل
                        </button>
                        <button type="button" class="btn" onclick="clearEntryForm()">
                            <i class="fas fa-eraser"></i> مسح النموذج
                        </button>
                    </div>
                </form>
            `;

            container.innerHTML = html;
        }

        // إرسال السجل
        function submitRecord(event, tableName) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const data = {};

            for (let [key, value] = formData.entries()) {
                if (value !== '') {
                    // تحويل أنواع البيانات
                    const fieldSchema = db.tables[tableName].schema[key];
                    if (fieldSchema.type === 'INTEGER') {
                        data[key] = parseInt(value);
                    } else if (fieldSchema.type === 'DECIMAL') {
                        data[key] = parseFloat(value);
                    } else if (fieldSchema.type === 'BOOLEAN') {
                        data[key] = value === 'true';
                    } else {
                        data[key] = value;
                    }
                }
            }

            try {
                const result = db.insert(tableName, data);
                showAlert(`تم إضافة السجل بنجاح في جدول ${getTableDisplayName(tableName)}`, 'success');
                clearEntryForm();
                refreshOverview();
            } catch (error) {
                showAlert('خطأ في إضافة السجل: ' + error.message, 'error');
            }
        }

        // مسح نموذج الإدخال
        function clearEntryForm() {
            const form = document.getElementById('entry-form');
            if (form) form.reset();
        }

        // حذف سجل
        function deleteRecord(tableName, id) {
            if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
                try {
                    db.delete(tableName, id);
                    showAlert('تم حذف السجل بنجاح', 'success');
                    loadTableData();
                    refreshOverview();
                } catch (error) {
                    showAlert('خطأ في حذف السجل: ' + error.message, 'error');
                }
            }
        }

        // تنفيذ استعلام
        function executeQuery() {
            const query = document.getElementById('custom-query').value.trim();
            if (!query) {
                showAlert('يرجى إدخال استعلام', 'warning');
                return;
            }

            const resultsContainer = document.getElementById('query-results');

            try {
                // تحليل الاستعلام وتنفيذه (مبسط)
                const result = executeCustomQuery(query);

                if (Array.isArray(result)) {
                    resultsContainer.innerHTML = `
                        <strong>النتائج (${result.length} سجل):</strong><br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                } else {
                    resultsContainer.innerHTML = `
                        <strong>النتيجة:</strong><br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultsContainer.innerHTML = `
                    <strong style="color: red;">خطأ:</strong><br>
                    <pre>${error.message}</pre>
                `;
            }
        }

        // تنفيذ استعلام مخصص (مبسط)
        function executeCustomQuery(query) {
            // تحليل بسيط للاستعلامات
            const lowerQuery = query.toLowerCase().trim();

            if (lowerQuery.startsWith('select')) {
                // استعلام SELECT مبسط
                const tableMatch = query.match(/from\s+(\w+)/i);
                if (tableMatch) {
                    const tableName = tableMatch[1];
                    return db.select(tableName);
                }
            }

            throw new Error('نوع الاستعلام غير مدعوم أو صيغة خاطئة');
        }

        // إنشاء بيانات تجريبية
        function createSampleData() {
            if (!db) return;

            try {
                // إضافة عملاء تجريبيين
                const customers = [
                    {
                        customerCode: 'CUST001',
                        name: 'أحمد محمد علي',
                        phone: '0551234567',
                        email: '<EMAIL>',
                        address: 'الرياض، المملكة العربية السعودية',
                        nationalId: '1234567890',
                        customerType: 'فرد'
                    },
                    {
                        customerCode: 'CUST002',
                        name: 'شركة النقل السريع',
                        phone: '0557654321',
                        email: '<EMAIL>',
                        address: 'جدة، المملكة العربية السعودية',
                        nationalId: '9876543210',
                        customerType: 'شركة'
                    }
                ];

                customers.forEach(customer => {
                    try {
                        db.insert('customers', customer);
                    } catch (e) {
                        // تجاهل الأخطاء إذا كانت البيانات موجودة
                    }
                });

                // إضافة موظفين تجريبيين
                const employees = [
                    {
                        employeeCode: 'EMP001',
                        name: 'محمد أحمد',
                        position: 'فني تركيب',
                        department: 'الفنيين',
                        phone: '0559876543',
                        email: '<EMAIL>',
                        nationalId: '1111111111',
                        hireDate: '2024-01-01'
                    }
                ];

                employees.forEach(employee => {
                    try {
                        db.insert('employees', employee);
                    } catch (e) {
                        // تجاهل الأخطاء
                    }
                });

                showAlert('تم إنشاء البيانات التجريبية بنجاح', 'success');
                refreshOverview();

            } catch (error) {
                showAlert('خطأ في إنشاء البيانات التجريبية: ' + error.message, 'error');
            }
        }

        // إنشاء نسخة احتياطية
        function createBackup() {
            if (!db) return;

            try {
                const backup = db.backup();
                showAlert('تم إنشاء نسخة احتياطية بنجاح', 'success');
                updateBackupList();
            } catch (error) {
                showAlert('خطأ في إنشاء النسخة الاحتياطية: ' + error.message, 'error');
            }
        }

        // تحديث قائمة النسخ الاحتياطية
        function updateBackupList() {
            if (!db) return;

            const container = document.getElementById('backup-list');

            if (db.backups.length === 0) {
                container.innerHTML = `
                    <div class="alert info">
                        <i class="fas fa-info-circle"></i> لا توجد نسخ احتياطية
                    </div>
                `;
                return;
            }

            let html = '<h4>النسخ الاحتياطية المتاحة:</h4>';

            db.backups.forEach((backup, index) => {
                html += `
                    <div class="table-card">
                        <h5>نسخة احتياطية ${index + 1}</h5>
                        <div class="table-info">
                            <span>التاريخ:</span>
                            <span class="table-info-value">${new Date(backup.timestamp).toLocaleString('ar-SA')}</span>
                        </div>
                        <div class="table-info">
                            <span>الإصدار:</span>
                            <span class="table-info-value">${backup.version}</span>
                        </div>
                        <div style="margin-top: 15px;">
                            <button class="btn success" onclick="restoreBackup(${index})">
                                <i class="fas fa-undo"></i> استعادة
                            </button>
                            <button class="btn info" onclick="downloadBackup(${index})">
                                <i class="fas fa-download"></i> تحميل
                            </button>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // استعادة نسخة احتياطية
        function restoreBackup(index) {
            if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                try {
                    db.restore(index);
                    showAlert('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                    refreshOverview();
                } catch (error) {
                    showAlert('خطأ في استعادة النسخة الاحتياطية: ' + error.message, 'error');
                }
            }
        }

        console.log('✅ تم تحميل واجهة إدارة قاعدة البيانات');
    </script>
</body>
</html>
