/**
 * نظام إدارة قاعدة البيانات الاحترافي
 * Professional Database Management System
 * مؤسسة وقود المستقبل - Future Fuel Corporation
 */

class DatabaseManager {
    constructor() {
        this.dbName = 'FutureFuelDB';
        this.version = 1.0;
        this.tables = {};
        this.indexes = {};
        this.relationships = {};
        this.constraints = {};
        this.triggers = {};
        this.backups = [];
        
        this.init();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    init() {
        this.createTables();
        this.createIndexes();
        this.createRelationships();
        this.createConstraints();
        this.createTriggers();
        this.loadData();
        
        console.log('✅ تم تهيئة قاعدة البيانات بنجاح');
    }

    /**
     * إنشاء الجداول
     */
    createTables() {
        // جدول العملاء
        this.tables.customers = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                customerCode: { type: 'VARCHAR(20)', unique: true, required: true },
                name: { type: 'VARCHAR(100)', required: true },
                phone: { type: 'VARCHAR(20)', required: true },
                email: { type: 'VARCHAR(100)', unique: true },
                address: { type: 'TEXT' },
                nationalId: { type: 'VARCHAR(20)', unique: true },
                customerType: { type: 'ENUM', values: ['فرد', 'شركة'], default: 'فرد' },
                creditLimit: { type: 'DECIMAL(10,2)', default: 0 },
                currentBalance: { type: 'DECIMAL(10,2)', default: 0 },
                status: { type: 'ENUM', values: ['نشط', 'معطل', 'محظور'], default: 'نشط' },
                registrationDate: { type: 'DATETIME', default: 'NOW()' },
                lastUpdate: { type: 'DATETIME', default: 'NOW()' },
                notes: { type: 'TEXT' }
            },
            data: []
        };

        // جدول بطاقات الغاز
        this.tables.gasCards = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                cardNumber: { type: 'VARCHAR(20)', unique: true, required: true },
                customerId: { type: 'INTEGER', foreignKey: 'customers.id', required: true },
                cardType: { type: 'ENUM', values: ['عادية', 'ذهبية', 'بلاتينية'], default: 'عادية' },
                balance: { type: 'DECIMAL(10,2)', default: 0 },
                creditLimit: { type: 'DECIMAL(10,2)', default: 0 },
                status: { type: 'ENUM', values: ['نشطة', 'معطلة', 'مفقودة', 'منتهية'], default: 'نشطة' },
                issueDate: { type: 'DATE', required: true },
                expiryDate: { type: 'DATE', required: true },
                lastUsed: { type: 'DATETIME' },
                securityCode: { type: 'VARCHAR(10)' },
                dailyLimit: { type: 'DECIMAL(10,2)', default: 1000 },
                monthlyLimit: { type: 'DECIMAL(10,2)', default: 10000 },
                notes: { type: 'TEXT' }
            },
            data: []
        };

        // جدول الإرسال (عمليات التركيب والمراقبة)
        this.tables.transmissionTable = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                operationNumber: { type: 'VARCHAR(20)', unique: true, required: true },
                operationType: { type: 'ENUM', values: ['تركيب', 'مراقبة'], required: true },
                tankNumber: { type: 'VARCHAR(30)', required: true },
                carType: { type: 'VARCHAR(50)', required: true },
                carModel: { type: 'VARCHAR(50)' },
                carYear: { type: 'INTEGER' },
                serialNumber: { type: 'VARCHAR(50)', required: true },
                registrationNumber: { type: 'VARCHAR(20)', required: true },
                ownerName: { type: 'VARCHAR(100)', required: true },
                ownerPhone: { type: 'VARCHAR(20)' },
                ownerNationalId: { type: 'VARCHAR(20)' },
                operationDate: { type: 'DATE', required: true },
                operationTime: { type: 'TIME', default: 'NOW()' },
                technicianId: { type: 'INTEGER', foreignKey: 'employees.id' },
                inspectorId: { type: 'INTEGER', foreignKey: 'employees.id' },
                certificateNumber: { type: 'VARCHAR(30)' },
                certificateDate: { type: 'DATE' },
                validUntil: { type: 'DATE' },
                operationCost: { type: 'DECIMAL(10,2)', default: 0 },
                paymentStatus: { type: 'ENUM', values: ['مدفوع', 'غير مدفوع', 'مؤجل'], default: 'غير مدفوع' },
                status: { type: 'ENUM', values: ['مكتمل', 'قيد التنفيذ', 'ملغي'], default: 'قيد التنفيذ' },
                notes: { type: 'TEXT' },
                createdAt: { type: 'DATETIME', default: 'NOW()' },
                updatedAt: { type: 'DATETIME', default: 'NOW()' }
            },
            data: []
        };

        // جدول الموظفين
        this.tables.employees = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                employeeCode: { type: 'VARCHAR(20)', unique: true, required: true },
                name: { type: 'VARCHAR(100)', required: true },
                position: { type: 'VARCHAR(50)', required: true },
                department: { type: 'VARCHAR(50)', required: true },
                phone: { type: 'VARCHAR(20)', required: true },
                email: { type: 'VARCHAR(100)', unique: true },
                nationalId: { type: 'VARCHAR(20)', unique: true, required: true },
                hireDate: { type: 'DATE', required: true },
                salary: { type: 'DECIMAL(10,2)' },
                status: { type: 'ENUM', values: ['نشط', 'معطل', 'مستقيل'], default: 'نشط' },
                permissions: { type: 'JSON', default: '{}' },
                lastLogin: { type: 'DATETIME' },
                notes: { type: 'TEXT' }
            },
            data: []
        };

        // جدول المنتجات والخدمات
        this.tables.products = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                productCode: { type: 'VARCHAR(20)', unique: true, required: true },
                name: { type: 'VARCHAR(100)', required: true },
                category: { type: 'VARCHAR(50)', required: true },
                type: { type: 'ENUM', values: ['منتج', 'خدمة'], required: true },
                unit: { type: 'VARCHAR(20)', default: 'قطعة' },
                costPrice: { type: 'DECIMAL(10,2)', default: 0 },
                sellingPrice: { type: 'DECIMAL(10,2)', default: 0 },
                currentStock: { type: 'INTEGER', default: 0 },
                minStock: { type: 'INTEGER', default: 0 },
                maxStock: { type: 'INTEGER', default: 1000 },
                supplierId: { type: 'INTEGER', foreignKey: 'suppliers.id' },
                status: { type: 'ENUM', values: ['متاح', 'غير متاح', 'متوقف'], default: 'متاح' },
                description: { type: 'TEXT' },
                specifications: { type: 'JSON', default: '{}' },
                createdAt: { type: 'DATETIME', default: 'NOW()' },
                updatedAt: { type: 'DATETIME', default: 'NOW()' }
            },
            data: []
        };

        // جدول الموردين
        this.tables.suppliers = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                supplierCode: { type: 'VARCHAR(20)', unique: true, required: true },
                name: { type: 'VARCHAR(100)', required: true },
                contactPerson: { type: 'VARCHAR(100)' },
                phone: { type: 'VARCHAR(20)', required: true },
                email: { type: 'VARCHAR(100)' },
                address: { type: 'TEXT' },
                taxNumber: { type: 'VARCHAR(20)' },
                paymentTerms: { type: 'VARCHAR(50)', default: 'نقدي' },
                creditLimit: { type: 'DECIMAL(10,2)', default: 0 },
                currentBalance: { type: 'DECIMAL(10,2)', default: 0 },
                status: { type: 'ENUM', values: ['نشط', 'معطل'], default: 'نشط' },
                rating: { type: 'INTEGER', default: 5, min: 1, max: 5 },
                notes: { type: 'TEXT' },
                createdAt: { type: 'DATETIME', default: 'NOW()' }
            },
            data: []
        };

        // جدول المبيعات
        this.tables.sales = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                saleNumber: { type: 'VARCHAR(20)', unique: true, required: true },
                customerId: { type: 'INTEGER', foreignKey: 'customers.id', required: true },
                employeeId: { type: 'INTEGER', foreignKey: 'employees.id', required: true },
                saleDate: { type: 'DATETIME', default: 'NOW()' },
                totalAmount: { type: 'DECIMAL(10,2)', required: true },
                discountAmount: { type: 'DECIMAL(10,2)', default: 0 },
                taxAmount: { type: 'DECIMAL(10,2)', default: 0 },
                netAmount: { type: 'DECIMAL(10,2)', required: true },
                paymentMethod: { type: 'ENUM', values: ['نقدي', 'بطاقة', 'تحويل', 'آجل'], default: 'نقدي' },
                paymentStatus: { type: 'ENUM', values: ['مدفوع', 'غير مدفوع', 'جزئي'], default: 'غير مدفوع' },
                status: { type: 'ENUM', values: ['مكتمل', 'ملغي', 'مرتجع'], default: 'مكتمل' },
                notes: { type: 'TEXT' }
            },
            data: []
        };

        // جدول تفاصيل المبيعات
        this.tables.saleItems = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                saleId: { type: 'INTEGER', foreignKey: 'sales.id', required: true },
                productId: { type: 'INTEGER', foreignKey: 'products.id', required: true },
                quantity: { type: 'DECIMAL(10,2)', required: true },
                unitPrice: { type: 'DECIMAL(10,2)', required: true },
                totalPrice: { type: 'DECIMAL(10,2)', required: true },
                discountPercent: { type: 'DECIMAL(5,2)', default: 0 },
                discountAmount: { type: 'DECIMAL(10,2)', default: 0 },
                netPrice: { type: 'DECIMAL(10,2)', required: true }
            },
            data: []
        };

        // جدول المشتريات
        this.tables.purchases = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                purchaseNumber: { type: 'VARCHAR(20)', unique: true, required: true },
                supplierId: { type: 'INTEGER', foreignKey: 'suppliers.id', required: true },
                employeeId: { type: 'INTEGER', foreignKey: 'employees.id', required: true },
                purchaseDate: { type: 'DATETIME', default: 'NOW()' },
                totalAmount: { type: 'DECIMAL(10,2)', required: true },
                discountAmount: { type: 'DECIMAL(10,2)', default: 0 },
                taxAmount: { type: 'DECIMAL(10,2)', default: 0 },
                netAmount: { type: 'DECIMAL(10,2)', required: true },
                paymentStatus: { type: 'ENUM', values: ['مدفوع', 'غير مدفوع', 'جزئي'], default: 'غير مدفوع' },
                status: { type: 'ENUM', values: ['مكتمل', 'ملغي', 'مرتجع'], default: 'مكتمل' },
                notes: { type: 'TEXT' }
            },
            data: []
        };

        // جدول المواعيد
        this.tables.appointments = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                appointmentNumber: { type: 'VARCHAR(20)', unique: true, required: true },
                customerId: { type: 'INTEGER', foreignKey: 'customers.id', required: true },
                serviceType: { type: 'VARCHAR(50)', required: true },
                appointmentDate: { type: 'DATE', required: true },
                appointmentTime: { type: 'TIME', required: true },
                duration: { type: 'INTEGER', default: 60 }, // بالدقائق
                technicianId: { type: 'INTEGER', foreignKey: 'employees.id' },
                status: { type: 'ENUM', values: ['مجدول', 'مؤكد', 'مكتمل', 'ملغي', 'مؤجل'], default: 'مجدول' },
                priority: { type: 'ENUM', values: ['عادي', 'مهم', 'عاجل'], default: 'عادي' },
                estimatedCost: { type: 'DECIMAL(10,2)', default: 0 },
                actualCost: { type: 'DECIMAL(10,2)', default: 0 },
                notes: { type: 'TEXT' },
                reminderSent: { type: 'BOOLEAN', default: false },
                createdAt: { type: 'DATETIME', default: 'NOW()' },
                updatedAt: { type: 'DATETIME', default: 'NOW()' }
            },
            data: []
        };

        // جدول المعاملات المالية
        this.tables.transactions = {
            schema: {
                id: { type: 'INTEGER', primaryKey: true, autoIncrement: true },
                transactionNumber: { type: 'VARCHAR(20)', unique: true, required: true },
                type: { type: 'ENUM', values: ['دخل', 'خرج', 'تحويل'], required: true },
                category: { type: 'VARCHAR(50)', required: true },
                amount: { type: 'DECIMAL(10,2)', required: true },
                description: { type: 'TEXT', required: true },
                referenceType: { type: 'VARCHAR(20)' }, // sales, purchases, etc.
                referenceId: { type: 'INTEGER' },
                paymentMethod: { type: 'VARCHAR(20)', default: 'نقدي' },
                transactionDate: { type: 'DATETIME', default: 'NOW()' },
                employeeId: { type: 'INTEGER', foreignKey: 'employees.id', required: true },
                status: { type: 'ENUM', values: ['مكتمل', 'معلق', 'ملغي'], default: 'مكتمل' },
                notes: { type: 'TEXT' }
            },
            data: []
        };

        console.log('✅ تم إنشاء جميع الجداول');
    }

    /**
     * إنشاء الفهارس
     */
    createIndexes() {
        this.indexes = {
            // فهارس العملاء
            'idx_customers_code': { table: 'customers', columns: ['customerCode'] },
            'idx_customers_phone': { table: 'customers', columns: ['phone'] },
            'idx_customers_email': { table: 'customers', columns: ['email'] },
            'idx_customers_national_id': { table: 'customers', columns: ['nationalId'] },
            
            // فهارس بطاقات الغاز
            'idx_gas_cards_number': { table: 'gasCards', columns: ['cardNumber'] },
            'idx_gas_cards_customer': { table: 'gasCards', columns: ['customerId'] },
            'idx_gas_cards_status': { table: 'gasCards', columns: ['status'] },
            
            // فهارس جدول الإرسال
            'idx_transmission_operation_number': { table: 'transmissionTable', columns: ['operationNumber'] },
            'idx_transmission_tank_number': { table: 'transmissionTable', columns: ['tankNumber'] },
            'idx_transmission_registration': { table: 'transmissionTable', columns: ['registrationNumber'] },
            'idx_transmission_date': { table: 'transmissionTable', columns: ['operationDate'] },
            'idx_transmission_type': { table: 'transmissionTable', columns: ['operationType'] },
            
            // فهارس المبيعات
            'idx_sales_number': { table: 'sales', columns: ['saleNumber'] },
            'idx_sales_customer': { table: 'sales', columns: ['customerId'] },
            'idx_sales_date': { table: 'sales', columns: ['saleDate'] },
            'idx_sales_status': { table: 'sales', columns: ['status'] },
            
            // فهارس المواعيد
            'idx_appointments_date': { table: 'appointments', columns: ['appointmentDate'] },
            'idx_appointments_customer': { table: 'appointments', columns: ['customerId'] },
            'idx_appointments_status': { table: 'appointments', columns: ['status'] }
        };

        console.log('✅ تم إنشاء الفهارس');
    }

    /**
     * إنشاء العلاقات
     */
    createRelationships() {
        this.relationships = {
            // علاقة العملاء ببطاقات الغاز (واحد لمتعدد)
            'customers_gas_cards': {
                type: 'ONE_TO_MANY',
                parent: 'customers',
                child: 'gasCards',
                parentKey: 'id',
                childKey: 'customerId'
            },
            
            // علاقة العملاء بالمبيعات (واحد لمتعدد)
            'customers_sales': {
                type: 'ONE_TO_MANY',
                parent: 'customers',
                child: 'sales',
                parentKey: 'id',
                childKey: 'customerId'
            },
            
            // علاقة الموظفين بالمبيعات (واحد لمتعدد)
            'employees_sales': {
                type: 'ONE_TO_MANY',
                parent: 'employees',
                child: 'sales',
                parentKey: 'id',
                childKey: 'employeeId'
            },
            
            // علاقة المبيعات بتفاصيل المبيعات (واحد لمتعدد)
            'sales_items': {
                type: 'ONE_TO_MANY',
                parent: 'sales',
                child: 'saleItems',
                parentKey: 'id',
                childKey: 'saleId'
            },
            
            // علاقة المنتجات بتفاصيل المبيعات (واحد لمتعدد)
            'products_sale_items': {
                type: 'ONE_TO_MANY',
                parent: 'products',
                child: 'saleItems',
                parentKey: 'id',
                childKey: 'productId'
            },
            
            // علاقة الموردين بالمنتجات (واحد لمتعدد)
            'suppliers_products': {
                type: 'ONE_TO_MANY',
                parent: 'suppliers',
                child: 'products',
                parentKey: 'id',
                childKey: 'supplierId'
            }
        };

        console.log('✅ تم إنشاء العلاقات');
    }

    /**
     * إنشاء القيود
     */
    createConstraints() {
        this.constraints = {
            // قيود العملاء
            'customers_phone_format': {
                table: 'customers',
                type: 'CHECK',
                condition: 'phone REGEXP "^[0-9+\\-\\s]+$"'
            },
            'customers_email_format': {
                table: 'customers',
                type: 'CHECK',
                condition: 'email REGEXP "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"'
            },
            
            // قيود بطاقات الغاز
            'gas_cards_balance_positive': {
                table: 'gasCards',
                type: 'CHECK',
                condition: 'balance >= 0'
            },
            'gas_cards_expiry_future': {
                table: 'gasCards',
                type: 'CHECK',
                condition: 'expiryDate > issueDate'
            },
            
            // قيود المبيعات
            'sales_amounts_positive': {
                table: 'sales',
                type: 'CHECK',
                condition: 'totalAmount >= 0 AND netAmount >= 0'
            },
            
            // قيود المواعيد
            'appointments_future_date': {
                table: 'appointments',
                type: 'CHECK',
                condition: 'appointmentDate >= CURDATE()'
            }
        };

        console.log('✅ تم إنشاء القيود');
    }

    /**
     * إنشاء المحفزات (Triggers)
     */
    createTriggers() {
        this.triggers = {
            // تحديث تاريخ آخر تعديل
            'update_timestamp': {
                tables: ['customers', 'gasCards', 'transmissionTable', 'products'],
                event: 'BEFORE_UPDATE',
                action: (record) => {
                    record.updatedAt = new Date().toISOString();
                    return record;
                }
            },
            
            // تحديث رصيد العميل عند المبيعات
            'update_customer_balance': {
                table: 'sales',
                event: 'AFTER_INSERT',
                action: (record) => {
                    this.updateCustomerBalance(record.customerId, record.netAmount);
                }
            },
            
            // تحديث المخزون عند المبيعات
            'update_stock_on_sale': {
                table: 'saleItems',
                event: 'AFTER_INSERT',
                action: (record) => {
                    this.updateProductStock(record.productId, -record.quantity);
                }
            }
        };

        console.log('✅ تم إنشاء المحفزات');
    }

    /**
     * تحميل البيانات من localStorage
     */
    loadData() {
        try {
            // تحميل بيانات جدول الإرسال
            const transmissionData = JSON.parse(localStorage.getItem('transmissionTableData') || '[]');
            this.tables.transmissionTable.data = transmissionData;
            
            // تحميل البيانات الرئيسية
            const mainData = JSON.parse(localStorage.getItem('gasShopData') || '{}');
            
            // توزيع البيانات على الجداول المناسبة
            Object.keys(mainData).forEach(key => {
                if (this.tables[key]) {
                    this.tables[key].data = mainData[key] || [];
                }
            });
            
            console.log('✅ تم تحميل البيانات من التخزين المحلي');
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error);
        }
    }

    /**
     * حفظ البيانات في localStorage
     */
    saveData() {
        try {
            // حفظ بيانات جدول الإرسال
            localStorage.setItem('transmissionTableData', JSON.stringify(this.tables.transmissionTable.data));
            
            // تجميع البيانات الرئيسية
            const mainData = {};
            Object.keys(this.tables).forEach(tableName => {
                if (tableName !== 'transmissionTable') {
                    mainData[tableName] = this.tables[tableName].data;
                }
            });
            
            localStorage.setItem('gasShopData', JSON.stringify(mainData));
            
            console.log('✅ تم حفظ البيانات في التخزين المحلي');
            return true;
        } catch (error) {
            console.error('❌ خطأ في حفظ البيانات:', error);
            return false;
        }
    }
}

    /**
     * إدراج سجل جديد
     */
    insert(tableName, data) {
        try {
            if (!this.tables[tableName]) {
                throw new Error(`الجدول ${tableName} غير موجود`);
            }

            const table = this.tables[tableName];
            const schema = table.schema;

            // التحقق من صحة البيانات
            this.validateData(tableName, data);

            // إنشاء سجل جديد
            const record = { ...data };

            // تعيين المفتاح الأساسي التلقائي
            const primaryKey = this.getPrimaryKey(tableName);
            if (primaryKey && schema[primaryKey].autoIncrement) {
                record[primaryKey] = this.getNextId(tableName);
            }

            // تعيين القيم الافتراضية
            this.setDefaultValues(tableName, record);

            // تطبيق المحفزات قبل الإدراج
            this.executeTriggers(tableName, 'BEFORE_INSERT', record);

            // إضافة السجل
            table.data.push(record);

            // تطبيق المحفزات بعد الإدراج
            this.executeTriggers(tableName, 'AFTER_INSERT', record);

            // حفظ البيانات
            this.saveData();

            console.log(`✅ تم إدراج سجل جديد في جدول ${tableName}`);
            return record;

        } catch (error) {
            console.error(`❌ خطأ في إدراج البيانات في جدول ${tableName}:`, error);
            throw error;
        }
    }

    /**
     * تحديث سجل
     */
    update(tableName, id, data) {
        try {
            if (!this.tables[tableName]) {
                throw new Error(`الجدول ${tableName} غير موجود`);
            }

            const table = this.tables[tableName];
            const primaryKey = this.getPrimaryKey(tableName);

            // البحث عن السجل
            const recordIndex = table.data.findIndex(record => record[primaryKey] === id);
            if (recordIndex === -1) {
                throw new Error(`السجل غير موجود`);
            }

            // التحقق من صحة البيانات
            this.validateData(tableName, data, true);

            // تحديث السجل
            const updatedRecord = { ...table.data[recordIndex], ...data };

            // تطبيق المحفزات قبل التحديث
            this.executeTriggers(tableName, 'BEFORE_UPDATE', updatedRecord);

            table.data[recordIndex] = updatedRecord;

            // تطبيق المحفزات بعد التحديث
            this.executeTriggers(tableName, 'AFTER_UPDATE', updatedRecord);

            // حفظ البيانات
            this.saveData();

            console.log(`✅ تم تحديث السجل في جدول ${tableName}`);
            return updatedRecord;

        } catch (error) {
            console.error(`❌ خطأ في تحديث البيانات في جدول ${tableName}:`, error);
            throw error;
        }
    }

    /**
     * حذف سجل
     */
    delete(tableName, id) {
        try {
            if (!this.tables[tableName]) {
                throw new Error(`الجدول ${tableName} غير موجود`);
            }

            const table = this.tables[tableName];
            const primaryKey = this.getPrimaryKey(tableName);

            // البحث عن السجل
            const recordIndex = table.data.findIndex(record => record[primaryKey] === id);
            if (recordIndex === -1) {
                throw new Error(`السجل غير موجود`);
            }

            const record = table.data[recordIndex];

            // تطبيق المحفزات قبل الحذف
            this.executeTriggers(tableName, 'BEFORE_DELETE', record);

            // حذف السجل
            table.data.splice(recordIndex, 1);

            // تطبيق المحفزات بعد الحذف
            this.executeTriggers(tableName, 'AFTER_DELETE', record);

            // حفظ البيانات
            this.saveData();

            console.log(`✅ تم حذف السجل من جدول ${tableName}`);
            return true;

        } catch (error) {
            console.error(`❌ خطأ في حذف البيانات من جدول ${tableName}:`, error);
            throw error;
        }
    }

    /**
     * البحث والاستعلام
     */
    select(tableName, conditions = {}, options = {}) {
        try {
            if (!this.tables[tableName]) {
                throw new Error(`الجدول ${tableName} غير موجود`);
            }

            let results = [...this.tables[tableName].data];

            // تطبيق شروط البحث
            if (Object.keys(conditions).length > 0) {
                results = results.filter(record => {
                    return Object.keys(conditions).every(key => {
                        const condition = conditions[key];
                        const value = record[key];

                        if (typeof condition === 'object') {
                            // شروط متقدمة
                            if (condition.$eq !== undefined) return value === condition.$eq;
                            if (condition.$ne !== undefined) return value !== condition.$ne;
                            if (condition.$gt !== undefined) return value > condition.$gt;
                            if (condition.$gte !== undefined) return value >= condition.$gte;
                            if (condition.$lt !== undefined) return value < condition.$lt;
                            if (condition.$lte !== undefined) return value <= condition.$lte;
                            if (condition.$in !== undefined) return condition.$in.includes(value);
                            if (condition.$nin !== undefined) return !condition.$nin.includes(value);
                            if (condition.$like !== undefined) return value && value.toString().includes(condition.$like);
                            if (condition.$regex !== undefined) return new RegExp(condition.$regex).test(value);
                        } else {
                            // شرط بسيط
                            return value === condition;
                        }
                        return true;
                    });
                });
            }

            // ترتيب النتائج
            if (options.orderBy) {
                const { field, direction = 'ASC' } = options.orderBy;
                results.sort((a, b) => {
                    const aVal = a[field];
                    const bVal = b[field];

                    if (direction === 'DESC') {
                        return bVal > aVal ? 1 : bVal < aVal ? -1 : 0;
                    } else {
                        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
                    }
                });
            }

            // تحديد النتائج
            if (options.limit) {
                const offset = options.offset || 0;
                results = results.slice(offset, offset + options.limit);
            }

            // اختيار حقول محددة
            if (options.select) {
                results = results.map(record => {
                    const selected = {};
                    options.select.forEach(field => {
                        selected[field] = record[field];
                    });
                    return selected;
                });
            }

            return results;

        } catch (error) {
            console.error(`❌ خطأ في الاستعلام من جدول ${tableName}:`, error);
            throw error;
        }
    }

    /**
     * البحث عن سجل واحد
     */
    findOne(tableName, conditions = {}) {
        const results = this.select(tableName, conditions, { limit: 1 });
        return results.length > 0 ? results[0] : null;
    }

    /**
     * عد السجلات
     */
    count(tableName, conditions = {}) {
        const results = this.select(tableName, conditions);
        return results.length;
    }

// تصدير الكلاس للاستخدام العام
window.DatabaseManager = DatabaseManager;
