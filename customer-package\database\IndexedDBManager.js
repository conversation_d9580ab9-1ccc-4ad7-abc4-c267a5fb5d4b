/**
 * نظام قاعدة بيانات IndexedDB للمساحة الكبيرة
 * IndexedDB Database System for Large Storage
 * مؤسسة وقود المستقبل - Future Fuel Corporation
 * 
 * المساحة المتاحة: 50% من مساحة القرص الصلب أو حتى عدة GB
 */

class IndexedDBManager {
    constructor() {
        this.dbName = 'FutureFuelDB_Large';
        this.version = 1;
        this.db = null;
        this.isReady = false;
        
        // تقدير المساحة المتاحة
        this.estimatedQuota = 0;
        this.usedSpace = 0;
        
        this.init();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    async init() {
        try {
            await this.checkStorageQuota();
            await this.openDatabase();
            console.log('✅ تم تهيئة قاعدة بيانات IndexedDB بنجاح');
            console.log(`📊 المساحة المتاحة: ${(this.estimatedQuota / 1024 / 1024 / 1024).toFixed(2)} GB`);
        } catch (error) {
            console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
        }
    }

    /**
     * فحص المساحة المتاحة
     */
    async checkStorageQuota() {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            const estimate = await navigator.storage.estimate();
            this.estimatedQuota = estimate.quota || 0;
            this.usedSpace = estimate.usage || 0;
            
            console.log(`📊 إحصائيات التخزين:`);
            console.log(`   المساحة الإجمالية: ${(this.estimatedQuota / 1024 / 1024 / 1024).toFixed(2)} GB`);
            console.log(`   المساحة المستخدمة: ${(this.usedSpace / 1024 / 1024).toFixed(2)} MB`);
            console.log(`   المساحة المتبقية: ${((this.estimatedQuota - this.usedSpace) / 1024 / 1024 / 1024).toFixed(2)} GB`);
        }
    }

    /**
     * فتح قاعدة البيانات
     */
    async openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => {
                reject(new Error('فشل في فتح قاعدة البيانات'));
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                this.isReady = true;
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                this.createStores(db);
            };
        });
    }

    /**
     * إنشاء المخازن (الجداول)
     */
    createStores(db) {
        // مخزن العملاء
        if (!db.objectStoreNames.contains('customers')) {
            const customerStore = db.createObjectStore('customers', { keyPath: 'id', autoIncrement: true });
            customerStore.createIndex('customerCode', 'customerCode', { unique: true });
            customerStore.createIndex('phone', 'phone', { unique: false });
            customerStore.createIndex('email', 'email', { unique: true });
            customerStore.createIndex('nationalId', 'nationalId', { unique: true });
        }

        // مخزن بطاقات الغاز
        if (!db.objectStoreNames.contains('gasCards')) {
            const cardStore = db.createObjectStore('gasCards', { keyPath: 'id', autoIncrement: true });
            cardStore.createIndex('cardNumber', 'cardNumber', { unique: true });
            cardStore.createIndex('customerId', 'customerId', { unique: false });
            cardStore.createIndex('status', 'status', { unique: false });
        }

        // مخزن جدول الإرسال
        if (!db.objectStoreNames.contains('transmissionTable')) {
            const transmissionStore = db.createObjectStore('transmissionTable', { keyPath: 'id', autoIncrement: true });
            transmissionStore.createIndex('operationNumber', 'operationNumber', { unique: true });
            transmissionStore.createIndex('tankNumber', 'tankNumber', { unique: false });
            transmissionStore.createIndex('operationDate', 'operationDate', { unique: false });
            transmissionStore.createIndex('operationType', 'operationType', { unique: false });
            transmissionStore.createIndex('registrationNumber', 'registrationNumber', { unique: false });
        }

        // مخزن الموظفين
        if (!db.objectStoreNames.contains('employees')) {
            const employeeStore = db.createObjectStore('employees', { keyPath: 'id', autoIncrement: true });
            employeeStore.createIndex('employeeCode', 'employeeCode', { unique: true });
            employeeStore.createIndex('nationalId', 'nationalId', { unique: true });
        }

        // مخزن المنتجات
        if (!db.objectStoreNames.contains('products')) {
            const productStore = db.createObjectStore('products', { keyPath: 'id', autoIncrement: true });
            productStore.createIndex('productCode', 'productCode', { unique: true });
            productStore.createIndex('category', 'category', { unique: false });
        }

        // مخزن المبيعات
        if (!db.objectStoreNames.contains('sales')) {
            const salesStore = db.createObjectStore('sales', { keyPath: 'id', autoIncrement: true });
            salesStore.createIndex('saleNumber', 'saleNumber', { unique: true });
            salesStore.createIndex('customerId', 'customerId', { unique: false });
            salesStore.createIndex('saleDate', 'saleDate', { unique: false });
        }

        // مخزن المواعيد
        if (!db.objectStoreNames.contains('appointments')) {
            const appointmentStore = db.createObjectStore('appointments', { keyPath: 'id', autoIncrement: true });
            appointmentStore.createIndex('appointmentNumber', 'appointmentNumber', { unique: true });
            appointmentStore.createIndex('customerId', 'customerId', { unique: false });
            appointmentStore.createIndex('appointmentDate', 'appointmentDate', { unique: false });
        }

        // مخزن الملفات الكبيرة (للمرفقات والصور)
        if (!db.objectStoreNames.contains('files')) {
            const fileStore = db.createObjectStore('files', { keyPath: 'id', autoIncrement: true });
            fileStore.createIndex('fileName', 'fileName', { unique: false });
            fileStore.createIndex('fileType', 'fileType', { unique: false });
            fileStore.createIndex('relatedTable', 'relatedTable', { unique: false });
            fileStore.createIndex('relatedId', 'relatedId', { unique: false });
        }

        // مخزن الأرشيف
        if (!db.objectStoreNames.contains('archive')) {
            const archiveStore = db.createObjectStore('archive', { keyPath: 'id', autoIncrement: true });
            archiveStore.createIndex('originalTable', 'originalTable', { unique: false });
            archiveStore.createIndex('archiveDate', 'archiveDate', { unique: false });
            archiveStore.createIndex('year', 'year', { unique: false });
        }

        console.log('✅ تم إنشاء جميع المخازن في IndexedDB');
    }

    /**
     * إدراج بيانات
     */
    async insert(storeName, data) {
        if (!this.isReady) {
            throw new Error('قاعدة البيانات غير جاهزة');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            // إضافة تاريخ الإنشاء
            data.createdAt = new Date().toISOString();
            data.updatedAt = new Date().toISOString();
            
            const request = store.add(data);

            request.onsuccess = () => {
                resolve({ ...data, id: request.result });
            };

            request.onerror = () => {
                reject(new Error('فشل في إدراج البيانات'));
            };
        });
    }

    /**
     * تحديث بيانات
     */
    async update(storeName, id, data) {
        if (!this.isReady) {
            throw new Error('قاعدة البيانات غير جاهزة');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            // الحصول على السجل الحالي أولاً
            const getRequest = store.get(id);
            
            getRequest.onsuccess = () => {
                const existingData = getRequest.result;
                if (!existingData) {
                    reject(new Error('السجل غير موجود'));
                    return;
                }
                
                // دمج البيانات الجديدة مع الموجودة
                const updatedData = { 
                    ...existingData, 
                    ...data, 
                    id: id,
                    updatedAt: new Date().toISOString()
                };
                
                const putRequest = store.put(updatedData);
                
                putRequest.onsuccess = () => {
                    resolve(updatedData);
                };
                
                putRequest.onerror = () => {
                    reject(new Error('فشل في تحديث البيانات'));
                };
            };
            
            getRequest.onerror = () => {
                reject(new Error('فشل في الحصول على السجل'));
            };
        });
    }

    /**
     * حذف بيانات
     */
    async delete(storeName, id) {
        if (!this.isReady) {
            throw new Error('قاعدة البيانات غير جاهزة');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            const request = store.delete(id);

            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = () => {
                reject(new Error('فشل في حذف البيانات'));
            };
        });
    }

    /**
     * البحث والاستعلام
     */
    async select(storeName, conditions = {}, options = {}) {
        if (!this.isReady) {
            throw new Error('قاعدة البيانات غير جاهزة');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const results = [];

            let request;
            
            // إذا كان هناك شرط واحد على فهرس
            if (Object.keys(conditions).length === 1) {
                const [key, value] = Object.entries(conditions)[0];
                
                if (store.indexNames.contains(key)) {
                    const index = store.index(key);
                    request = index.openCursor(IDBKeyRange.only(value));
                } else {
                    request = store.openCursor();
                }
            } else {
                request = store.openCursor();
            }

            request.onsuccess = (event) => {
                const cursor = event.target.result;
                
                if (cursor) {
                    const record = cursor.value;
                    
                    // تطبيق الشروط
                    if (this.matchesConditions(record, conditions)) {
                        results.push(record);
                    }
                    
                    cursor.continue();
                } else {
                    // تطبيق الخيارات (ترتيب، تحديد)
                    let finalResults = this.applyOptions(results, options);
                    resolve(finalResults);
                }
            };

            request.onerror = () => {
                reject(new Error('فشل في الاستعلام'));
            };
        });
    }

    /**
     * البحث عن سجل واحد
     */
    async findOne(storeName, conditions = {}) {
        const results = await this.select(storeName, conditions, { limit: 1 });
        return results.length > 0 ? results[0] : null;
    }

    /**
     * عد السجلات
     */
    async count(storeName, conditions = {}) {
        const results = await this.select(storeName, conditions);
        return results.length;
    }

    /**
     * التحقق من تطابق الشروط
     */
    matchesConditions(record, conditions) {
        return Object.keys(conditions).every(key => {
            const condition = conditions[key];
            const value = record[key];
            
            if (typeof condition === 'object' && condition !== null) {
                // شروط متقدمة
                if (condition.$eq !== undefined) return value === condition.$eq;
                if (condition.$ne !== undefined) return value !== condition.$ne;
                if (condition.$gt !== undefined) return value > condition.$gt;
                if (condition.$gte !== undefined) return value >= condition.$gte;
                if (condition.$lt !== undefined) return value < condition.$lt;
                if (condition.$lte !== undefined) return value <= condition.$lte;
                if (condition.$in !== undefined) return condition.$in.includes(value);
                if (condition.$nin !== undefined) return !condition.$nin.includes(value);
                if (condition.$like !== undefined) return value && value.toString().includes(condition.$like);
            } else {
                // شرط بسيط
                return value === condition;
            }
            return true;
        });
    }

    /**
     * تطبيق خيارات الاستعلام
     */
    applyOptions(results, options) {
        let finalResults = [...results];
        
        // ترتيب
        if (options.orderBy) {
            const { field, direction = 'ASC' } = options.orderBy;
            finalResults.sort((a, b) => {
                const aVal = a[field];
                const bVal = b[field];
                
                if (direction === 'DESC') {
                    return bVal > aVal ? 1 : bVal < aVal ? -1 : 0;
                } else {
                    return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
                }
            });
        }
        
        // تحديد النتائج
        if (options.limit) {
            const offset = options.offset || 0;
            finalResults = finalResults.slice(offset, offset + options.limit);
        }
        
        // اختيار حقول محددة
        if (options.select) {
            finalResults = finalResults.map(record => {
                const selected = {};
                options.select.forEach(field => {
                    selected[field] = record[field];
                });
                return selected;
            });
        }
        
        return finalResults;
    }

    /**
     * حفظ ملف كبير
     */
    async saveFile(fileName, fileData, relatedTable = null, relatedId = null) {
        const fileRecord = {
            fileName: fileName,
            fileData: fileData, // يمكن أن يكون ArrayBuffer أو Blob
            fileSize: fileData.size || fileData.byteLength,
            fileType: fileName.split('.').pop(),
            relatedTable: relatedTable,
            relatedId: relatedId,
            uploadDate: new Date().toISOString()
        };
        
        return await this.insert('files', fileRecord);
    }

    /**
     * أرشفة البيانات القديمة
     */
    async archiveOldData(tableName, olderThanDays = 365) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
        const cutoffDateString = cutoffDate.toISOString().split('T')[0];
        
        // البحث عن البيانات القديمة
        const oldRecords = await this.select(tableName, {
            createdAt: { $lt: cutoffDateString }
        });
        
        // نقل إلى الأرشيف
        for (let record of oldRecords) {
            await this.insert('archive', {
                originalTable: tableName,
                originalData: record,
                archiveDate: new Date().toISOString(),
                year: new Date(record.createdAt).getFullYear()
            });
            
            // حذف من الجدول الأصلي
            await this.delete(tableName, record.id);
        }
        
        return oldRecords.length;
    }

    /**
     * الحصول على إحصائيات التخزين
     */
    async getStorageStats() {
        await this.checkStorageQuota();
        
        const stats = {
            totalQuota: this.estimatedQuota,
            usedSpace: this.usedSpace,
            availableSpace: this.estimatedQuota - this.usedSpace,
            quotaGB: (this.estimatedQuota / 1024 / 1024 / 1024).toFixed(2),
            usedMB: (this.usedSpace / 1024 / 1024).toFixed(2),
            availableGB: ((this.estimatedQuota - this.usedSpace) / 1024 / 1024 / 1024).toFixed(2),
            tables: {}
        };
        
        // إحصائيات كل جدول
        const tableNames = ['customers', 'gasCards', 'transmissionTable', 'employees', 'products', 'sales', 'appointments', 'files', 'archive'];
        
        for (let tableName of tableNames) {
            try {
                const count = await this.count(tableName);
                stats.tables[tableName] = { recordCount: count };
            } catch (error) {
                stats.tables[tableName] = { recordCount: 0 };
            }
        }
        
        return stats;
    }
}

// تصدير الكلاس
window.IndexedDBManager = IndexedDBManager;

console.log('✅ تم تحميل نظام قاعدة البيانات IndexedDB للمساحة الكبيرة');
