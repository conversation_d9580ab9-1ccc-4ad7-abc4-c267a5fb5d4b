<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاحص قاعدة البيانات - مؤسسة وقود المستقبل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #2196f3, #1976d2);
            color: white;
            border-radius: 10px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #2196f3;
            border-bottom: 2px solid #2196f3;
            padding-bottom: 10px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: right;
        }
        .data-table th {
            background: #2196f3;
            color: white;
            font-weight: bold;
        }
        .data-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .btn {
            background: #2196f3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #1976d2;
        }
        .btn.danger {
            background: #f44336;
        }
        .btn.danger:hover {
            background: #d32f2f;
        }
        .btn.success {
            background: #4caf50;
        }
        .btn.success:hover {
            background: #388e3c;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #2196f3;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .json-viewer {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            direction: ltr;
            text-align: left;
        }
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .alert.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .alert.warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ff9800;
        }
        .alert.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-database"></i> فاحص قاعدة البيانات</h1>
            <p>أداة شاملة لفحص وإدارة بيانات مؤسسة وقود المستقبل</p>
        </div>

        <!-- إحصائيات عامة -->
        <div class="section">
            <h3><i class="fas fa-chart-bar"></i> الإحصائيات العامة</h3>
            <div class="stats" id="general-stats"></div>
            <button class="btn" onclick="refreshStats()"><i class="fas fa-sync"></i> تحديث الإحصائيات</button>
        </div>

        <!-- بيانات جدول الإرسال -->
        <div class="section">
            <h3><i class="fas fa-table"></i> بيانات جدول الإرسال</h3>
            <div id="transmission-data"></div>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="loadTransmissionData()"><i class="fas fa-download"></i> تحميل البيانات</button>
                <button class="btn success" onclick="addSampleTransmissionData()"><i class="fas fa-plus"></i> إضافة بيانات تجريبية</button>
                <button class="btn danger" onclick="clearTransmissionData()"><i class="fas fa-trash"></i> مسح البيانات</button>
                <button class="btn" onclick="exportTransmissionData()"><i class="fas fa-file-export"></i> تصدير JSON</button>
            </div>
        </div>

        <!-- البيانات الرئيسية -->
        <div class="section">
            <h3><i class="fas fa-cogs"></i> البيانات الرئيسية للنظام</h3>
            <div id="main-data"></div>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="loadMainData()"><i class="fas fa-download"></i> تحميل البيانات الرئيسية</button>
                <button class="btn" onclick="initializeMainData()"><i class="fas fa-cog"></i> تهيئة البيانات</button>
                <button class="btn danger" onclick="clearAllData()"><i class="fas fa-exclamation-triangle"></i> مسح جميع البيانات</button>
            </div>
        </div>

        <!-- إعدادات التطبيق -->
        <div class="section">
            <h3><i class="fas fa-sliders-h"></i> إعدادات التطبيق</h3>
            <div id="config-data"></div>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="loadConfigData()"><i class="fas fa-download"></i> تحميل الإعدادات</button>
                <button class="btn success" onclick="resetConfig()"><i class="fas fa-undo"></i> إعادة تعيين الإعدادات</button>
            </div>
        </div>

        <!-- أدوات الصيانة -->
        <div class="section">
            <h3><i class="fas fa-tools"></i> أدوات الصيانة</h3>
            <div style="margin: 15px 0;">
                <button class="btn" onclick="backupData()"><i class="fas fa-save"></i> نسخ احتياطي</button>
                <button class="btn" onclick="validateData()"><i class="fas fa-check-circle"></i> التحقق من صحة البيانات</button>
                <button class="btn" onclick="optimizeStorage()"><i class="fas fa-compress"></i> تحسين التخزين</button>
                <button class="btn" onclick="showStorageInfo()"><i class="fas fa-info-circle"></i> معلومات التخزين</button>
            </div>
            <div id="maintenance-results"></div>
        </div>
    </div>

    <script>
        // تحديث الإحصائيات العامة
        function refreshStats() {
            const statsContainer = document.getElementById('general-stats');
            
            try {
                const transmissionData = JSON.parse(localStorage.getItem('transmissionTableData') || '[]');
                const mainData = JSON.parse(localStorage.getItem('gasShopData') || '{}');
                const configData = JSON.parse(localStorage.getItem('appConfig') || '{}');
                
                const now = new Date();
                const currentMonth = now.getMonth();
                const currentYear = now.getFullYear();
                
                // حساب إحصائيات جدول الإرسال
                const monthTransmissions = transmissionData.filter(entry => {
                    const entryDate = new Date(entry.operationDate);
                    return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
                }).length;
                
                const installationCount = transmissionData.filter(entry => entry.type === 'تركيب').length;
                const monitoringCount = transmissionData.filter(entry => entry.type === 'مراقبة').length;
                
                // حساب حجم التخزين
                const storageSize = JSON.stringify(localStorage).length;
                const storageSizeKB = (storageSize / 1024).toFixed(2);
                
                statsContainer.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${transmissionData.length}</div>
                        <div class="stat-label">إجمالي عمليات الإرسال</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${monthTransmissions}</div>
                        <div class="stat-label">عمليات الشهر الحالي</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${installationCount}</div>
                        <div class="stat-label">عمليات التركيب</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${monitoringCount}</div>
                        <div class="stat-label">عمليات المراقبة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${Object.keys(mainData).length}</div>
                        <div class="stat-label">أقسام البيانات الرئيسية</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${storageSizeKB} KB</div>
                        <div class="stat-label">حجم التخزين المستخدم</div>
                    </div>
                `;
                
            } catch (error) {
                statsContainer.innerHTML = `
                    <div class="alert error">
                        خطأ في تحميل الإحصائيات: ${error.message}
                    </div>
                `;
            }
        }

        // تحميل بيانات جدول الإرسال
        function loadTransmissionData() {
            const container = document.getElementById('transmission-data');
            
            try {
                const data = JSON.parse(localStorage.getItem('transmissionTableData') || '[]');
                
                if (data.length === 0) {
                    container.innerHTML = `
                        <div class="alert warning">
                            <i class="fas fa-exclamation-triangle"></i> لا توجد بيانات في جدول الإرسال
                        </div>
                    `;
                    return;
                }
                
                let tableHTML = `
                    <div class="alert success">
                        <i class="fas fa-check-circle"></i> تم العثور على ${data.length} سجل في جدول الإرسال
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>نوع العملية</th>
                                <th>رقم الخزان</th>
                                <th>صنف السيارة</th>
                                <th>الرقم التسلسلي</th>
                                <th>رقم التسجيل</th>
                                <th>اسم المالك</th>
                                <th>تاريخ العملية</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                data.forEach(entry => {
                    tableHTML += `
                        <tr>
                            <td>${entry.type || 'غير محدد'}</td>
                            <td>${entry.tankNumber || 'غير محدد'}</td>
                            <td>${entry.carType || 'غير محدد'}</td>
                            <td>${entry.serialNumber || 'غير محدد'}</td>
                            <td>${entry.registrationNumber || 'غير محدد'}</td>
                            <td>${entry.ownerName || 'غير محدد'}</td>
                            <td>${entry.operationDate || 'غير محدد'}</td>
                        </tr>
                    `;
                });
                
                tableHTML += `
                        </tbody>
                    </table>
                    <div class="json-viewer">${JSON.stringify(data, null, 2)}</div>
                `;
                
                container.innerHTML = tableHTML;
                
            } catch (error) {
                container.innerHTML = `
                    <div class="alert error">
                        خطأ في تحميل بيانات جدول الإرسال: ${error.message}
                    </div>
                `;
            }
        }

        // إضافة بيانات تجريبية لجدول الإرسال
        function addSampleTransmissionData() {
            if (confirm('هل تريد إضافة بيانات تجريبية لجدول الإرسال؟')) {
                const sampleData = [
                    {
                        type: 'تركيب',
                        tankNumber: 'TK-2024-001',
                        carType: 'تويوتا كورولا 2020',
                        serialNumber: 'SN789123',
                        registrationNumber: '12345-16-89',
                        ownerName: 'أحمد محمد علي',
                        operationDate: '2024-12-19'
                    },
                    {
                        type: 'مراقبة',
                        tankNumber: 'TK-2024-002',
                        carType: 'هيونداي إلنترا 2019',
                        serialNumber: 'SN456789',
                        registrationNumber: '67890-16-12',
                        ownerName: 'فاطمة أحمد',
                        operationDate: '2024-12-18'
                    },
                    {
                        type: 'تركيب',
                        tankNumber: 'TK-2024-003',
                        carType: 'نيسان صني 2021',
                        serialNumber: 'SN321654',
                        registrationNumber: '54321-16-98',
                        ownerName: 'محمد عبد الله',
                        operationDate: '2024-12-17'
                    }
                ];
                
                localStorage.setItem('transmissionTableData', JSON.stringify(sampleData));
                
                // تحديث البيانات الرئيسية
                const mainData = JSON.parse(localStorage.getItem('gasShopData') || '{}');
                mainData.transmissionTable = sampleData;
                localStorage.setItem('gasShopData', JSON.stringify(mainData));
                
                alert('تم إضافة البيانات التجريبية بنجاح!');
                loadTransmissionData();
                refreshStats();
            }
        }

        // مسح بيانات جدول الإرسال
        function clearTransmissionData() {
            if (confirm('هل أنت متأكد من مسح جميع بيانات جدول الإرسال؟\nهذا الإجراء لا يمكن التراجع عنه!')) {
                localStorage.removeItem('transmissionTableData');
                
                // مسح من البيانات الرئيسية
                const mainData = JSON.parse(localStorage.getItem('gasShopData') || '{}');
                delete mainData.transmissionTable;
                localStorage.setItem('gasShopData', JSON.stringify(mainData));
                
                alert('تم مسح بيانات جدول الإرسال!');
                loadTransmissionData();
                refreshStats();
            }
        }

        // تصدير بيانات جدول الإرسال
        function exportTransmissionData() {
            try {
                const data = JSON.parse(localStorage.getItem('transmissionTableData') || '[]');
                const dataStr = JSON.stringify(data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `transmission-table-data-${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                alert('تم تصدير البيانات بنجاح!');
            } catch (error) {
                alert('خطأ في تصدير البيانات: ' + error.message);
            }
        }

        // تحميل البيانات الرئيسية
        function loadMainData() {
            const container = document.getElementById('main-data');
            
            try {
                const data = JSON.parse(localStorage.getItem('gasShopData') || '{}');
                
                container.innerHTML = `
                    <div class="alert success">
                        <i class="fas fa-check-circle"></i> البيانات الرئيسية تحتوي على ${Object.keys(data).length} قسم
                    </div>
                    <div class="json-viewer">${JSON.stringify(data, null, 2)}</div>
                `;
                
            } catch (error) {
                container.innerHTML = `
                    <div class="alert error">
                        خطأ في تحميل البيانات الرئيسية: ${error.message}
                    </div>
                `;
            }
        }

        // تحميل إعدادات التطبيق
        function loadConfigData() {
            const container = document.getElementById('config-data');
            
            try {
                const data = JSON.parse(localStorage.getItem('appConfig') || '{}');
                
                container.innerHTML = `
                    <div class="alert success">
                        <i class="fas fa-check-circle"></i> إعدادات التطبيق تحتوي على ${Object.keys(data).length} إعداد
                    </div>
                    <div class="json-viewer">${JSON.stringify(data, null, 2)}</div>
                `;
                
            } catch (error) {
                container.innerHTML = `
                    <div class="alert error">
                        خطأ في تحميل إعدادات التطبيق: ${error.message}
                    </div>
                `;
            }
        }

        // عرض معلومات التخزين
        function showStorageInfo() {
            const container = document.getElementById('maintenance-results');
            
            try {
                const storageInfo = {
                    totalSize: JSON.stringify(localStorage).length,
                    itemCount: localStorage.length,
                    items: {}
                };
                
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    const value = localStorage.getItem(key);
                    storageInfo.items[key] = {
                        size: value.length,
                        sizeKB: (value.length / 1024).toFixed(2)
                    };
                }
                
                container.innerHTML = `
                    <div class="alert success">
                        <h4>معلومات التخزين:</h4>
                        <p><strong>الحجم الإجمالي:</strong> ${(storageInfo.totalSize / 1024).toFixed(2)} KB</p>
                        <p><strong>عدد العناصر:</strong> ${storageInfo.itemCount}</p>
                    </div>
                    <div class="json-viewer">${JSON.stringify(storageInfo, null, 2)}</div>
                `;
                
            } catch (error) {
                container.innerHTML = `
                    <div class="alert error">
                        خطأ في عرض معلومات التخزين: ${error.message}
                    </div>
                `;
            }
        }

        // التحقق من صحة البيانات
        function validateData() {
            const container = document.getElementById('maintenance-results');
            const issues = [];
            
            try {
                // فحص بيانات جدول الإرسال
                const transmissionData = JSON.parse(localStorage.getItem('transmissionTableData') || '[]');
                transmissionData.forEach((entry, index) => {
                    if (!entry.type) issues.push(`جدول الإرسال - السجل ${index + 1}: نوع العملية مفقود`);
                    if (!entry.tankNumber) issues.push(`جدول الإرسال - السجل ${index + 1}: رقم الخزان مفقود`);
                    if (!entry.operationDate) issues.push(`جدول الإرسال - السجل ${index + 1}: تاريخ العملية مفقود`);
                });
                
                if (issues.length === 0) {
                    container.innerHTML = `
                        <div class="alert success">
                            <i class="fas fa-check-circle"></i> جميع البيانات صحيحة ولا توجد مشاكل!
                        </div>
                    `;
                } else {
                    container.innerHTML = `
                        <div class="alert warning">
                            <h4>تم العثور على ${issues.length} مشكلة:</h4>
                            <ul>
                                ${issues.map(issue => `<li>${issue}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                }
                
            } catch (error) {
                container.innerHTML = `
                    <div class="alert error">
                        خطأ في التحقق من البيانات: ${error.message}
                    </div>
                `;
            }
        }

        // تشغيل الفحوصات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
            loadTransmissionData();
            loadMainData();
            loadConfigData();
        });
    </script>
</body>
</html>
