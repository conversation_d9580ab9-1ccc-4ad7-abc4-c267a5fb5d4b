<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص تكامل جدول الإرسال</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .check-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .check-item.success {
            background: #e8f5e8;
            border-color: #4caf50;
        }
        .check-item.error {
            background: #ffebee;
            border-color: #f44336;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .status.ok {
            background: #4caf50;
            color: white;
        }
        .status.fail {
            background: #f44336;
            color: white;
        }
        .btn {
            background: #2196f3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #1976d2;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #2196f3;
            border-radius: 10px;
        }
        .section h3 {
            margin-top: 0;
            color: #2196f3;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-check-circle"></i> فحص تكامل جدول الإرسال</h1>
        <p>هذه الصفحة تتحقق من صحة تكامل جدول الإرسال مع النظام الرئيسي</p>

        <div class="section">
            <h3>📁 فحص الملفات</h3>
            <div id="file-checks"></div>
        </div>

        <div class="section">
            <h3>🔗 فحص الروابط</h3>
            <div id="link-checks"></div>
            <button class="btn" onclick="testLinks()">اختبار الروابط</button>
        </div>

        <div class="section">
            <h3>💾 فحص البيانات</h3>
            <div id="data-checks"></div>
            <button class="btn" onclick="testData()">اختبار البيانات</button>
        </div>

        <div class="section">
            <h3>🚀 تشغيل سريع</h3>
            <button class="btn" onclick="openDashboard()">
                <i class="fas fa-home"></i> فتح النظام الرئيسي
            </button>
            <button class="btn" onclick="openTransmissionTable()">
                <i class="fas fa-table"></i> فتح جدول الإرسال
            </button>
            <button class="btn" onclick="openTestPage()">
                <i class="fas fa-flask"></i> صفحة الاختبار
            </button>
        </div>
    </div>

    <script>
        // فحص الملفات
        function checkFiles() {
            const fileChecks = document.getElementById('file-checks');
            const files = [
                { name: 'transmission-table.html', path: './transmission-table.html' },
                { name: 'app/dashboard.html', path: './app/dashboard.html' },
                { name: 'app/open-transmission-table.html', path: './app/open-transmission-table.html' },
                { name: 'test-transmission-integration.html', path: './test-transmission-integration.html' }
            ];

            fileChecks.innerHTML = '';
            
            files.forEach(file => {
                const checkItem = document.createElement('div');
                checkItem.className = 'check-item';
                
                // محاولة فحص الملف
                fetch(file.path)
                    .then(response => {
                        if (response.ok) {
                            checkItem.className += ' success';
                            checkItem.innerHTML = `
                                <span><i class="fas fa-file"></i> ${file.name}</span>
                                <span class="status ok">موجود</span>
                            `;
                        } else {
                            throw new Error('File not found');
                        }
                    })
                    .catch(() => {
                        checkItem.className += ' error';
                        checkItem.innerHTML = `
                            <span><i class="fas fa-file"></i> ${file.name}</span>
                            <span class="status fail">غير موجود</span>
                        `;
                    });
                
                fileChecks.appendChild(checkItem);
            });
        }

        // اختبار الروابط
        function testLinks() {
            const linkChecks = document.getElementById('link-checks');
            linkChecks.innerHTML = '';

            const links = [
                { name: 'رابط القائمة الجانبية', test: () => typeof openTransmissionTable === 'function' },
                { name: 'بطاقة لوحة التحكم', test: () => document.querySelector('.transmission-table') !== null },
                { name: 'دالة فتح جدول الإرسال', test: () => typeof openTransmissionTable === 'function' }
            ];

            links.forEach(link => {
                const checkItem = document.createElement('div');
                checkItem.className = 'check-item';
                
                try {
                    const result = link.test();
                    if (result) {
                        checkItem.className += ' success';
                        checkItem.innerHTML = `
                            <span><i class="fas fa-link"></i> ${link.name}</span>
                            <span class="status ok">يعمل</span>
                        `;
                    } else {
                        throw new Error('Test failed');
                    }
                } catch (e) {
                    checkItem.className += ' error';
                    checkItem.innerHTML = `
                        <span><i class="fas fa-link"></i> ${link.name}</span>
                        <span class="status fail">لا يعمل</span>
                    `;
                }
                
                linkChecks.appendChild(checkItem);
            });
        }

        // اختبار البيانات
        function testData() {
            const dataChecks = document.getElementById('data-checks');
            dataChecks.innerHTML = '';

            const tests = [
                { 
                    name: 'localStorage متاح', 
                    test: () => typeof Storage !== 'undefined' && localStorage 
                },
                { 
                    name: 'بيانات جدول الإرسال', 
                    test: () => {
                        const data = localStorage.getItem('transmissionTableData');
                        return data !== null;
                    }
                },
                { 
                    name: 'البيانات الرئيسية', 
                    test: () => {
                        const data = localStorage.getItem('gasShopData');
                        return data !== null;
                    }
                }
            ];

            tests.forEach(test => {
                const checkItem = document.createElement('div');
                checkItem.className = 'check-item';
                
                try {
                    const result = test.test();
                    if (result) {
                        checkItem.className += ' success';
                        checkItem.innerHTML = `
                            <span><i class="fas fa-database"></i> ${test.name}</span>
                            <span class="status ok">متاح</span>
                        `;
                    } else {
                        throw new Error('Test failed');
                    }
                } catch (e) {
                    checkItem.className += ' error';
                    checkItem.innerHTML = `
                        <span><i class="fas fa-database"></i> ${test.name}</span>
                        <span class="status fail">غير متاح</span>
                    `;
                }
                
                dataChecks.appendChild(checkItem);
            });
        }

        // فتح النظام الرئيسي
        function openDashboard() {
            window.open('./app/dashboard.html', '_blank');
        }

        // فتح جدول الإرسال
        function openTransmissionTable() {
            window.open('./transmission-table.html', '_blank');
        }

        // فتح صفحة الاختبار
        function openTestPage() {
            window.open('./test-transmission-integration.html', '_blank');
        }

        // تشغيل الفحوصات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkFiles();
            testLinks();
            testData();
        });
    </script>
</body>
</html>
