@echo off
chcp 65001 >nul
title إصلاح تثبيت مؤسسة وقود المستقبل
color 0A

echo.
echo ================================================================================
echo                    إصلاح تثبيت مؤسسة وقود المستقبل
echo                    Future Fuel Corporation - Installation Fix
echo ================================================================================
echo.
echo تم اكتشاف مشكلة في التثبيت السابق
echo سيتم إصلاح المسارات والاختصارات الآن...
echo.

:: تحديد مجلد التثبيت
set "INSTALL_DIR=C:\Users\<USER>\Future Fuel Management"

:: التحقق من وجود المجلد
if not exist "%INSTALL_DIR%" (
    echo ❌ خطأ: مجلد التثبيت غير موجود
    echo يرجى إعادة تشغيل install.bat
    pause
    exit /b 1
)

echo 🔧 إصلاح ملف التطبيق الرئيسي...
(
echo @echo off
echo chcp 65001 ^>nul
echo title مؤسسة وقود المستقبل
echo cd /d "%INSTALL_DIR%"
echo if exist "app\dashboard.html" (
echo     start "" "app\dashboard.html"
echo ^) else if exist "app\index.html" (
echo     start "" "app\index.html"
echo ^) else (
echo     echo ❌ خطأ: ملفات التطبيق غير موجودة
echo     pause
echo ^)
) > "%INSTALL_DIR%\FutureFuel.bat"
echo ✓ تم إصلاح ملف التطبيق

echo.
echo 🖥️ إصلاح اختصار سطح المكتب...
set "DESKTOP=%USERPROFILE%\Desktop"
(
echo [InternetShortcut]
echo URL=file:///%INSTALL_DIR:\=/%/app/dashboard.html
echo IconFile=%INSTALL_DIR%\assets\icon.ico
echo IconIndex=0
) > "%DESKTOP%\مؤسسة وقود المستقبل.url"
echo ✓ تم إصلاح اختصار سطح المكتب

echo.
echo 📋 إصلاح اختصار قائمة ابدأ...
set "STARTMENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
if not exist "%STARTMENU%\مؤسسة وقود المستقبل" mkdir "%STARTMENU%\مؤسسة وقود المستقبل"
(
echo [InternetShortcut]
echo URL=file:///%INSTALL_DIR:\=/%/app/dashboard.html
echo IconFile=%INSTALL_DIR%\assets\icon.ico
echo IconIndex=0
) > "%STARTMENU%\مؤسسة وقود المستقبل\مؤسسة وقود المستقبل.url"
echo ✓ تم إصلاح اختصار قائمة ابدأ

echo.
echo 📁 التحقق من ملفات التطبيق...
if exist "%INSTALL_DIR%\app\dashboard.html" (
    echo ✅ dashboard.html موجود
) else (
    echo ❌ dashboard.html غير موجود
)

if exist "%INSTALL_DIR%\app\index.html" (
    echo ✅ index.html موجود
) else (
    echo ❌ index.html غير موجود
)

if exist "%INSTALL_DIR%\transmission-table.html" (
    echo ✅ transmission-table.html موجود
) else (
    echo ❌ transmission-table.html غير موجود
)

echo.
echo ================================================================================
echo                              اكتمل الإصلاح
echo ================================================================================
echo.
echo ✅ تم إصلاح جميع المسارات والاختصارات
echo.
echo يمكنك الآن تشغيل التطبيق من:
echo 🖥️ اختصار سطح المكتب: "مؤسسة وقود المستقبل"
echo 📋 قائمة ابدأ: ابحث عن "Future Fuel"
echo 📁 مباشرة: "%INSTALL_DIR%\FutureFuel.bat"
echo.

set /p "LAUNCH=هل تريد تشغيل التطبيق الآن؟ (Y/N): "
if /i "%LAUNCH%"=="Y" (
    echo.
    echo 🚀 تشغيل التطبيق...
    call "%INSTALL_DIR%\FutureFuel.bat"
) else if /i "%LAUNCH%"=="نعم" (
    echo.
    echo 🚀 تشغيل التطبيق...
    call "%INSTALL_DIR%\FutureFuel.bat"
)

echo.
echo شكراً لاستخدامك مؤسسة وقود المستقبل!
echo.
pause
