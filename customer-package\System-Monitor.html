<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مراقب النظام - مؤسسة وقود المستقبل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
            margin: 0;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #4caf50, #2e7d32);
            color: white;
            border-radius: 10px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .status-card h3 {
            margin-top: 0;
            color: #2196f3;
            border-bottom: 2px solid #2196f3;
            padding-bottom: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        .status-online {
            background: #4caf50;
            animation: pulse 2s infinite;
        }
        .status-offline {
            background: #f44336;
        }
        .status-warning {
            background: #ff9800;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .metric-value {
            font-weight: bold;
            color: #2196f3;
        }
        .btn {
            background: #2196f3;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 12px;
        }
        .btn:hover {
            background: #1976d2;
        }
        .btn.success {
            background: #4caf50;
        }
        .btn.warning {
            background: #ff9800;
        }
        .btn.danger {
            background: #f44336;
        }
        .log-area {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            direction: ltr;
            text-align: left;
        }
        .log-entry {
            margin: 5px 0;
            padding: 3px;
        }
        .log-info {
            color: #2196f3;
        }
        .log-success {
            color: #4caf50;
        }
        .log-warning {
            color: #ff9800;
        }
        .log-error {
            color: #f44336;
        }
        .real-time {
            font-size: 14px;
            color: #666;
            text-align: center;
            margin: 10px 0;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-desktop"></i> مراقب النظام</h1>
            <p>مراقبة شاملة لحالة نظام مؤسسة وقود المستقبل</p>
            <div class="real-time">
                آخر تحديث: <span id="last-update"></span>
            </div>
        </div>

        <div class="status-grid">
            <!-- حالة النظام الرئيسي -->
            <div class="status-card">
                <h3>
                    <span class="status-indicator" id="main-status"></span>
                    النظام الرئيسي
                </h3>
                <div class="metric">
                    <span>حالة التشغيل:</span>
                    <span class="metric-value" id="main-running">فحص...</span>
                </div>
                <div class="metric">
                    <span>آخر نشاط:</span>
                    <span class="metric-value" id="main-activity">فحص...</span>
                </div>
                <div class="metric">
                    <span>عدد المستخدمين:</span>
                    <span class="metric-value" id="main-users">فحص...</span>
                </div>
                <button class="btn" onclick="checkMainSystem()">فحص الحالة</button>
                <button class="btn success" onclick="openMainSystem()">فتح النظام</button>
            </div>

            <!-- حالة جدول الإرسال -->
            <div class="status-card">
                <h3>
                    <span class="status-indicator" id="transmission-status"></span>
                    جدول الإرسال
                </h3>
                <div class="metric">
                    <span>عدد السجلات:</span>
                    <span class="metric-value" id="transmission-records">فحص...</span>
                </div>
                <div class="metric">
                    <span>عمليات اليوم:</span>
                    <span class="metric-value" id="transmission-today">فحص...</span>
                </div>
                <div class="metric">
                    <span>آخر عملية:</span>
                    <span class="metric-value" id="transmission-last">فحص...</span>
                </div>
                <button class="btn" onclick="checkTransmissionTable()">فحص البيانات</button>
                <button class="btn success" onclick="openTransmissionTable()">فتح الجدول</button>
            </div>

            <!-- حالة قاعدة البيانات -->
            <div class="status-card">
                <h3>
                    <span class="status-indicator" id="database-status"></span>
                    قاعدة البيانات
                </h3>
                <div class="metric">
                    <span>حجم البيانات:</span>
                    <span class="metric-value" id="database-size">فحص...</span>
                </div>
                <div class="metric">
                    <span>عدد الجداول:</span>
                    <span class="metric-value" id="database-tables">فحص...</span>
                </div>
                <div class="metric">
                    <span>آخر نسخة احتياطية:</span>
                    <span class="metric-value" id="database-backup">فحص...</span>
                </div>
                <button class="btn" onclick="checkDatabase()">فحص قاعدة البيانات</button>
                <button class="btn warning" onclick="backupDatabase()">نسخ احتياطي</button>
            </div>

            <!-- حالة الأداء -->
            <div class="status-card">
                <h3>
                    <span class="status-indicator" id="performance-status"></span>
                    الأداء والموارد
                </h3>
                <div class="metric">
                    <span>استخدام الذاكرة:</span>
                    <span class="metric-value" id="memory-usage">فحص...</span>
                </div>
                <div class="metric">
                    <span>سرعة الاستجابة:</span>
                    <span class="metric-value" id="response-time">فحص...</span>
                </div>
                <div class="metric">
                    <span>حالة المتصفح:</span>
                    <span class="metric-value" id="browser-status">فحص...</span>
                </div>
                <button class="btn" onclick="checkPerformance()">فحص الأداء</button>
                <button class="btn warning" onclick="optimizeSystem()">تحسين النظام</button>
            </div>
        </div>

        <!-- سجل الأحداث -->
        <div class="status-card" style="margin-top: 20px;">
            <h3><i class="fas fa-list"></i> سجل الأحداث المباشر</h3>
            <div class="log-area" id="event-log"></div>
            <button class="btn" onclick="clearLog()">مسح السجل</button>
            <button class="btn success" onclick="exportLog()">تصدير السجل</button>
            <button class="btn" onclick="refreshAll()">تحديث شامل</button>
        </div>
    </div>

    <script>
        let logEntries = [];

        // إضافة إدخال للسجل
        function addLogEntry(message, type = 'info') {
            const timestamp = new Date().toLocaleString('ar-SA');
            const entry = {
                time: timestamp,
                message: message,
                type: type
            };
            
            logEntries.unshift(entry);
            if (logEntries.length > 100) {
                logEntries = logEntries.slice(0, 100);
            }
            
            updateLogDisplay();
        }

        // تحديث عرض السجل
        function updateLogDisplay() {
            const logArea = document.getElementById('event-log');
            logArea.innerHTML = logEntries.map(entry => 
                `<div class="log-entry log-${entry.type}">[${entry.time}] ${entry.message}</div>`
            ).join('');
            logArea.scrollTop = 0;
        }

        // فحص النظام الرئيسي
        function checkMainSystem() {
            addLogEntry('بدء فحص النظام الرئيسي...', 'info');
            
            try {
                // فحص localStorage
                const mainData = JSON.parse(localStorage.getItem('gasShopData') || '{}');
                const hasData = Object.keys(mainData).length > 0;
                
                // تحديث المؤشرات
                document.getElementById('main-status').className = 'status-indicator ' + (hasData ? 'status-online' : 'status-warning');
                document.getElementById('main-running').textContent = hasData ? 'يعمل بشكل طبيعي' : 'يحتاج تهيئة';
                document.getElementById('main-activity').textContent = new Date().toLocaleString('ar-SA');
                document.getElementById('main-users').textContent = '1 مستخدم نشط';
                
                addLogEntry(`النظام الرئيسي: ${hasData ? 'يعمل بشكل طبيعي' : 'يحتاج تهيئة'}`, hasData ? 'success' : 'warning');
                
            } catch (error) {
                document.getElementById('main-status').className = 'status-indicator status-offline';
                addLogEntry(`خطأ في فحص النظام الرئيسي: ${error.message}`, 'error');
            }
        }

        // فحص جدول الإرسال
        function checkTransmissionTable() {
            addLogEntry('بدء فحص جدول الإرسال...', 'info');
            
            try {
                const transmissionData = JSON.parse(localStorage.getItem('transmissionTableData') || '[]');
                const today = new Date().toISOString().split('T')[0];
                const todayOperations = transmissionData.filter(entry => entry.operationDate === today).length;
                const lastOperation = transmissionData.length > 0 ? transmissionData[transmissionData.length - 1].operationDate : 'لا توجد عمليات';
                
                // تحديث المؤشرات
                document.getElementById('transmission-status').className = 'status-indicator status-online';
                document.getElementById('transmission-records').textContent = transmissionData.length + ' سجل';
                document.getElementById('transmission-today').textContent = todayOperations + ' عملية';
                document.getElementById('transmission-last').textContent = lastOperation;
                
                addLogEntry(`جدول الإرسال: ${transmissionData.length} سجل، ${todayOperations} عملية اليوم`, 'success');
                
            } catch (error) {
                document.getElementById('transmission-status').className = 'status-indicator status-offline';
                addLogEntry(`خطأ في فحص جدول الإرسال: ${error.message}`, 'error');
            }
        }

        // فحص قاعدة البيانات
        function checkDatabase() {
            addLogEntry('بدء فحص قاعدة البيانات...', 'info');
            
            try {
                const totalSize = JSON.stringify(localStorage).length;
                const sizeKB = (totalSize / 1024).toFixed(2);
                const tableCount = localStorage.length;
                
                // تحديث المؤشرات
                document.getElementById('database-status').className = 'status-indicator status-online';
                document.getElementById('database-size').textContent = sizeKB + ' KB';
                document.getElementById('database-tables').textContent = tableCount + ' جدول';
                document.getElementById('database-backup').textContent = 'غير متاح';
                
                addLogEntry(`قاعدة البيانات: ${sizeKB} KB، ${tableCount} جدول`, 'success');
                
            } catch (error) {
                document.getElementById('database-status').className = 'status-indicator status-offline';
                addLogEntry(`خطأ في فحص قاعدة البيانات: ${error.message}`, 'error');
            }
        }

        // فحص الأداء
        function checkPerformance() {
            addLogEntry('بدء فحص الأداء...', 'info');
            
            try {
                const startTime = performance.now();
                
                // محاكاة عملية
                for (let i = 0; i < 1000; i++) {
                    Math.random();
                }
                
                const endTime = performance.now();
                const responseTime = (endTime - startTime).toFixed(2);
                
                // معلومات المتصفح
                const browserInfo = navigator.userAgent.includes('Chrome') ? 'Chrome' : 
                                  navigator.userAgent.includes('Firefox') ? 'Firefox' : 
                                  navigator.userAgent.includes('Safari') ? 'Safari' : 'متصفح آخر';
                
                // تحديث المؤشرات
                document.getElementById('performance-status').className = 'status-indicator status-online';
                document.getElementById('memory-usage').textContent = 'طبيعي';
                document.getElementById('response-time').textContent = responseTime + ' ms';
                document.getElementById('browser-status').textContent = browserInfo;
                
                addLogEntry(`الأداء: زمن الاستجابة ${responseTime} ms، المتصفح ${browserInfo}`, 'success');
                
            } catch (error) {
                document.getElementById('performance-status').className = 'status-indicator status-offline';
                addLogEntry(`خطأ في فحص الأداء: ${error.message}`, 'error');
            }
        }

        // فتح النظام الرئيسي
        function openMainSystem() {
            window.open('./app/dashboard.html', '_blank');
            addLogEntry('تم فتح النظام الرئيسي', 'info');
        }

        // فتح جدول الإرسال
        function openTransmissionTable() {
            window.open('./transmission-table.html', '_blank');
            addLogEntry('تم فتح جدول الإرسال', 'info');
        }

        // نسخ احتياطي لقاعدة البيانات
        function backupDatabase() {
            try {
                const allData = {};
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    allData[key] = localStorage.getItem(key);
                }
                
                const dataStr = JSON.stringify(allData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `backup-${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                addLogEntry('تم إنشاء نسخة احتياطية بنجاح', 'success');
                
            } catch (error) {
                addLogEntry(`خطأ في إنشاء النسخة الاحتياطية: ${error.message}`, 'error');
            }
        }

        // تحسين النظام
        function optimizeSystem() {
            addLogEntry('بدء تحسين النظام...', 'info');
            
            try {
                // تنظيف البيانات المؤقتة
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }
                
                // إجبار جمع القمامة (إذا كان متاحاً)
                if (window.gc) {
                    window.gc();
                }
                
                addLogEntry('تم تحسين النظام بنجاح', 'success');
                
            } catch (error) {
                addLogEntry(`خطأ في تحسين النظام: ${error.message}`, 'error');
            }
        }

        // مسح السجل
        function clearLog() {
            logEntries = [];
            updateLogDisplay();
            addLogEntry('تم مسح السجل', 'info');
        }

        // تصدير السجل
        function exportLog() {
            try {
                const logData = logEntries.map(entry => 
                    `[${entry.time}] ${entry.type.toUpperCase()}: ${entry.message}`
                ).join('\n');
                
                const dataBlob = new Blob([logData], {type: 'text/plain'});
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `system-log-${new Date().toISOString().split('T')[0]}.txt`;
                link.click();
                
                addLogEntry('تم تصدير السجل بنجاح', 'success');
                
            } catch (error) {
                addLogEntry(`خطأ في تصدير السجل: ${error.message}`, 'error');
            }
        }

        // تحديث شامل
        function refreshAll() {
            addLogEntry('بدء التحديث الشامل...', 'info');
            checkMainSystem();
            checkTransmissionTable();
            checkDatabase();
            checkPerformance();
            document.getElementById('last-update').textContent = new Date().toLocaleString('ar-SA');
            addLogEntry('اكتمل التحديث الشامل', 'success');
        }

        // تحديث الوقت
        function updateTime() {
            document.getElementById('last-update').textContent = new Date().toLocaleString('ar-SA');
        }

        // تشغيل الفحوصات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addLogEntry('تم تشغيل مراقب النظام', 'success');
            refreshAll();
            
            // تحديث تلقائي كل 30 ثانية
            setInterval(refreshAll, 30000);
            
            // تحديث الوقت كل ثانية
            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>
