/**
 * واجهة برمجة التطبيقات لقاعدة البيانات
 * Database API Interface
 * مؤسسة وقود المستقبل - Future Fuel Corporation
 */

class DatabaseAPI {
    constructor() {
        this.db = new DatabaseManager();
        this.apiVersion = '1.0';
        this.endpoints = {};
        this.middleware = [];
        
        this.initializeEndpoints();
        console.log('✅ تم تهيئة واجهة برمجة التطبيقات لقاعدة البيانات');
    }

    /**
     * تهيئة نقاط النهاية
     */
    initializeEndpoints() {
        // نقاط نهاية العملاء
        this.endpoints.customers = {
            get: (params) => this.getCustomers(params),
            post: (data) => this.createCustomer(data),
            put: (id, data) => this.updateCustomer(id, data),
            delete: (id) => this.deleteCustomer(id),
            getById: (id) => this.getCustomerById(id),
            search: (query) => this.searchCustomers(query)
        };

        // نقاط نهاية بطاقات الغاز
        this.endpoints.gasCards = {
            get: (params) => this.getGasCards(params),
            post: (data) => this.createGasCard(data),
            put: (id, data) => this.updateGasCard(id, data),
            delete: (id) => this.deleteGasCard(id),
            getByCustomer: (customerId) => this.getGasCardsByCustomer(customerId),
            activate: (id) => this.activateGasCard(id),
            deactivate: (id) => this.deactivateGasCard(id)
        };

        // نقاط نهاية جدول الإرسال
        this.endpoints.transmissionTable = {
            get: (params) => this.getTransmissionRecords(params),
            post: (data) => this.createTransmissionRecord(data),
            put: (id, data) => this.updateTransmissionRecord(id, data),
            delete: (id) => this.deleteTransmissionRecord(id),
            getByDate: (date) => this.getTransmissionByDate(date),
            getByType: (type) => this.getTransmissionByType(type),
            getStatistics: () => this.getTransmissionStatistics()
        };

        // نقاط نهاية المبيعات
        this.endpoints.sales = {
            get: (params) => this.getSales(params),
            post: (data) => this.createSale(data),
            put: (id, data) => this.updateSale(id, data),
            delete: (id) => this.deleteSale(id),
            getByCustomer: (customerId) => this.getSalesByCustomer(customerId),
            getByDate: (date) => this.getSalesByDate(date),
            getStatistics: () => this.getSalesStatistics()
        };

        // نقاط نهاية المواعيد
        this.endpoints.appointments = {
            get: (params) => this.getAppointments(params),
            post: (data) => this.createAppointment(data),
            put: (id, data) => this.updateAppointment(id, data),
            delete: (id) => this.deleteAppointment(id),
            getByDate: (date) => this.getAppointmentsByDate(date),
            getByCustomer: (customerId) => this.getAppointmentsByCustomer(customerId),
            confirm: (id) => this.confirmAppointment(id),
            cancel: (id) => this.cancelAppointment(id)
        };

        // نقاط نهاية التقارير
        this.endpoints.reports = {
            daily: (date) => this.getDailyReport(date),
            monthly: (month, year) => this.getMonthlyReport(month, year),
            yearly: (year) => this.getYearlyReport(year),
            custom: (startDate, endDate) => this.getCustomReport(startDate, endDate)
        };
    }

    /**
     * إضافة وسطاء (Middleware)
     */
    addMiddleware(middleware) {
        this.middleware.push(middleware);
    }

    /**
     * تنفيذ الوسطاء
     */
    executeMiddleware(request) {
        for (let middleware of this.middleware) {
            const result = middleware(request);
            if (result === false) {
                throw new Error('تم رفض الطلب بواسطة الوسطاء');
            }
        }
    }

    /**
     * معالج الطلبات الرئيسي
     */
    async handleRequest(endpoint, method, params = {}, data = null) {
        try {
            // تنفيذ الوسطاء
            this.executeMiddleware({ endpoint, method, params, data });

            // التحقق من وجود نقطة النهاية
            if (!this.endpoints[endpoint]) {
                throw new Error(`نقطة النهاية ${endpoint} غير موجودة`);
            }

            // التحقق من وجود الطريقة
            if (!this.endpoints[endpoint][method]) {
                throw new Error(`الطريقة ${method} غير مدعومة لنقطة النهاية ${endpoint}`);
            }

            // تنفيذ الطلب
            const result = await this.endpoints[endpoint][method](params, data);

            return {
                success: true,
                data: result,
                timestamp: new Date().toISOString(),
                apiVersion: this.apiVersion
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString(),
                apiVersion: this.apiVersion
            };
        }
    }

    // ==================== وظائف العملاء ====================

    getCustomers(params = {}) {
        const { page = 1, limit = 50, search, status } = params;
        let conditions = {};

        if (search) {
            // البحث في الاسم أو الهاتف أو البريد الإلكتروني
            conditions = {
                $or: [
                    { name: { $like: search } },
                    { phone: { $like: search } },
                    { email: { $like: search } }
                ]
            };
        }

        if (status) {
            conditions.status = status;
        }

        const offset = (page - 1) * limit;
        return this.db.select('customers', conditions, {
            limit: limit,
            offset: offset,
            orderBy: { field: 'registrationDate', direction: 'DESC' }
        });
    }

    createCustomer(data) {
        // التحقق من البيانات المطلوبة
        const required = ['name', 'phone'];
        for (let field of required) {
            if (!data[field]) {
                throw new Error(`الحقل ${field} مطلوب`);
            }
        }

        // إنشاء رمز العميل تلقائياً
        if (!data.customerCode) {
            data.customerCode = this.generateCustomerCode();
        }

        return this.db.insert('customers', data);
    }

    updateCustomer(id, data) {
        return this.db.update('customers', id, data);
    }

    deleteCustomer(id) {
        // التحقق من عدم وجود بطاقات غاز مرتبطة
        const gasCards = this.db.select('gasCards', { customerId: id });
        if (gasCards.length > 0) {
            throw new Error('لا يمكن حذف العميل لوجود بطاقات غاز مرتبطة به');
        }

        return this.db.delete('customers', id);
    }

    getCustomerById(id) {
        return this.db.findOne('customers', { id: id });
    }

    searchCustomers(query) {
        return this.db.select('customers', {
            $or: [
                { name: { $like: query } },
                { phone: { $like: query } },
                { email: { $like: query } },
                { customerCode: { $like: query } }
            ]
        });
    }

    generateCustomerCode() {
        const count = this.db.count('customers') + 1;
        return `CUST${count.toString().padStart(4, '0')}`;
    }

    // ==================== وظائف بطاقات الغاز ====================

    getGasCards(params = {}) {
        const { customerId, status, page = 1, limit = 50 } = params;
        let conditions = {};

        if (customerId) conditions.customerId = customerId;
        if (status) conditions.status = status;

        const offset = (page - 1) * limit;
        return this.db.select('gasCards', conditions, {
            limit: limit,
            offset: offset,
            orderBy: { field: 'issueDate', direction: 'DESC' }
        });
    }

    createGasCard(data) {
        // التحقق من البيانات المطلوبة
        const required = ['customerId', 'cardType'];
        for (let field of required) {
            if (!data[field]) {
                throw new Error(`الحقل ${field} مطلوب`);
            }
        }

        // إنشاء رقم البطاقة تلقائياً
        if (!data.cardNumber) {
            data.cardNumber = this.generateCardNumber();
        }

        // تعيين تاريخ الإصدار والانتهاء
        if (!data.issueDate) {
            data.issueDate = new Date().toISOString().split('T')[0];
        }

        if (!data.expiryDate) {
            const expiryDate = new Date();
            expiryDate.setFullYear(expiryDate.getFullYear() + 2); // صالحة لمدة سنتين
            data.expiryDate = expiryDate.toISOString().split('T')[0];
        }

        return this.db.insert('gasCards', data);
    }

    updateGasCard(id, data) {
        return this.db.update('gasCards', id, data);
    }

    deleteGasCard(id) {
        return this.db.delete('gasCards', id);
    }

    getGasCardsByCustomer(customerId) {
        return this.db.select('gasCards', { customerId: customerId });
    }

    activateGasCard(id) {
        return this.db.update('gasCards', id, { status: 'نشطة' });
    }

    deactivateGasCard(id) {
        return this.db.update('gasCards', id, { status: 'معطلة' });
    }

    generateCardNumber() {
        const count = this.db.count('gasCards') + 1;
        return `GC${count.toString().padStart(6, '0')}`;
    }

    // ==================== وظائف جدول الإرسال ====================

    getTransmissionRecords(params = {}) {
        const { type, date, page = 1, limit = 50 } = params;
        let conditions = {};

        if (type) conditions.operationType = type;
        if (date) conditions.operationDate = date;

        const offset = (page - 1) * limit;
        return this.db.select('transmissionTable', conditions, {
            limit: limit,
            offset: offset,
            orderBy: { field: 'operationDate', direction: 'DESC' }
        });
    }

    createTransmissionRecord(data) {
        // التحقق من البيانات المطلوبة
        const required = ['operationType', 'tankNumber', 'carType', 'serialNumber', 'registrationNumber', 'ownerName'];
        for (let field of required) {
            if (!data[field]) {
                throw new Error(`الحقل ${field} مطلوب`);
            }
        }

        // إنشاء رقم العملية تلقائياً
        if (!data.operationNumber) {
            data.operationNumber = this.generateOperationNumber();
        }

        // تعيين تاريخ العملية إذا لم يكن محدداً
        if (!data.operationDate) {
            data.operationDate = new Date().toISOString().split('T')[0];
        }

        return this.db.insert('transmissionTable', data);
    }

    updateTransmissionRecord(id, data) {
        return this.db.update('transmissionTable', id, data);
    }

    deleteTransmissionRecord(id) {
        return this.db.delete('transmissionTable', id);
    }

    getTransmissionByDate(date) {
        return this.db.select('transmissionTable', { operationDate: date });
    }

    getTransmissionByType(type) {
        return this.db.select('transmissionTable', { operationType: type });
    }

    getTransmissionStatistics() {
        const total = this.db.count('transmissionTable');
        const installations = this.db.count('transmissionTable', { operationType: 'تركيب' });
        const monitoring = this.db.count('transmissionTable', { operationType: 'مراقبة' });
        
        const today = new Date().toISOString().split('T')[0];
        const todayOperations = this.db.count('transmissionTable', { operationDate: today });

        return {
            total,
            installations,
            monitoring,
            todayOperations
        };
    }

    generateOperationNumber() {
        const count = this.db.count('transmissionTable') + 1;
        const year = new Date().getFullYear();
        return `OP${year}${count.toString().padStart(4, '0')}`;
    }

    // ==================== وظائف التقارير ====================

    getDailyReport(date) {
        const transmissionOps = this.db.select('transmissionTable', { operationDate: date });
        const sales = this.db.select('sales', { 
            saleDate: { $like: date } 
        });

        return {
            date,
            transmissionOperations: transmissionOps.length,
            sales: sales.length,
            totalSalesAmount: sales.reduce((sum, sale) => sum + (sale.netAmount || 0), 0),
            details: {
                transmissionOps,
                sales
            }
        };
    }

    getMonthlyReport(month, year) {
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
        const endDate = `${year}-${month.toString().padStart(2, '0')}-31`;

        const transmissionOps = this.db.select('transmissionTable', {
            operationDate: { $gte: startDate, $lte: endDate }
        });

        return {
            month,
            year,
            transmissionOperations: transmissionOps.length,
            installations: transmissionOps.filter(op => op.operationType === 'تركيب').length,
            monitoring: transmissionOps.filter(op => op.operationType === 'مراقبة').length
        };
    }
}

// تصدير الكلاس
window.DatabaseAPI = DatabaseAPI;

console.log('✅ تم تحميل واجهة برمجة التطبيقات لقاعدة البيانات');
